epoch: 2, train_loss: 12.9643, train_acc: 34.6988, train_fscore: 29.8102, test_loss: 12.5277, test_acc: 50.955, test_fscore: 46.244, time: 21.66 sec

*** 新的最优F1分数: 51.5793% (Epoch 3) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 31.2500% (144 样本)
sad: 73.8776% (245 样本)
neutral: 29.6875% (384 样本)
angry: 86.4706% (170 样本)
excited: 78.2609% (299 样本)
frustrated: 34.3832% (381 样本)

每个类别的F1分数:
happy: 30.6122%
sad: 72.9839%
neutral: 39.2427%
angry: 48.1178%
excited: 76.4706%
frustrated: 40.1840%

总体指标:
总体准确率: 52.4954%
加权准确率 (W_ACC, 按测试集分布): 52.4954%
总体F1分数 (不加权宏平均): 51.2685%
手动计算宏平均F1: 51.2685%
加权F1 (W_F1, 按测试集分布): 51.5793%
============================================================
epoch: 3, train_loss: 11.9531, train_acc: 48.9673, train_fscore: 44.7089, test_loss: 11.8711, test_acc: 52.4954, test_fscore: 51.5793, time: 20.55 sec

*** 新的最优F1分数: 59.4765% (Epoch 4) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 47.2222% (144 样本)
sad: 71.0204% (245 样本)
neutral: 65.8854% (384 样本)
angry: 71.7647% (170 样本)
excited: 71.9064% (299 样本)
frustrated: 35.1706% (381 样本)

每个类别的F1分数:
happy: 37.4656%
sad: 73.4177%
neutral: 58.0941%
angry: 63.0491%
excited: 75.3065%
frustrated: 46.2069%

总体指标:
总体准确率: 59.5194%
加权准确率 (W_ACC, 按测试集分布): 59.5194%
总体F1分数 (不加权宏平均): 58.9233%
手动计算宏平均F1: 58.9233%
加权F1 (W_F1, 按测试集分布): 59.4765%
============================================================
epoch: 4, train_loss: 11.4348, train_acc: 57.2289, train_fscore: 54.9854, test_loss: 11.6426, test_acc: 59.5194, test_fscore: 59.4765, time: 22.33 sec

*** 新的最优F1分数: 61.6796% (Epoch 5) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 48.6111% (144 样本)
sad: 77.9592% (245 样本)
neutral: 49.4792% (384 样本)
angry: 71.1765% (170 样本)
excited: 68.8963% (299 样本)
frustrated: 55.6430% (381 样本)

每个类别的F1分数:
happy: 36.0825%
sad: 74.6094%
neutral: 52.1262%
angry: 66.6667%
excited: 73.7030%
frustrated: 61.0072%

总体指标:
总体准确率: 60.9982%
加权准确率 (W_ACC, 按测试集分布): 60.9982%
总体F1分数 (不加权宏平均): 60.6992%
手动计算宏平均F1: 60.6992%
加权F1 (W_F1, 按测试集分布): 61.6796%
============================================================
epoch: 5, train_loss: 11.1337, train_acc: 56.1962, train_fscore: 52.7542, test_loss: 11.1851, test_acc: 60.9982, test_fscore: 61.6796, time: 21.93 sec

*** 新的最优F1分数: 64.7245% (Epoch 6) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 33.3333% (144 样本)
sad: 62.8571% (245 样本)
neutral: 60.6771% (384 样本)
angry: 81.1765% (170 样本)
excited: 87.6254% (299 样本)
frustrated: 58.2677% (381 样本)

每个类别的F1分数:
happy: 35.4244%
sad: 71.1316%
neutral: 61.5588%
angry: 67.3171%
excited: 80.6154%
frustrated: 61.2414%

总体指标:
总体准确率: 65.1263%
加权准确率 (W_ACC, 按测试集分布): 65.1263%
总体F1分数 (不加权宏平均): 62.8814%
手动计算宏平均F1: 62.8814%
加权F1 (W_F1, 按测试集分布): 64.7245%
============================================================
epoch: 6, train_loss: 10.9106, train_acc: 60.9294, train_fscore: 59.6875, test_loss: 11.0403, test_acc: 65.1263, test_fscore: 64.7245, time: 20.95 sec
epoch: 7, train_loss: 10.8133, train_acc: 59.0361, train_fscore: 56.9647, test_loss: 11.0647, test_acc: 60.1356, test_fscore: 59.7344, time: 21.66 sec

*** 新的最优F1分数: 66.3461% (Epoch 8) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 40.9722% (144 样本)
sad: 68.5714% (245 样本)
neutral: 56.2500% (384 样本)
angry: 66.4706% (170 样本)
excited: 84.2809% (299 样本)
frustrated: 70.8661% (381 样本)

每个类别的F1分数:
happy: 40.8304%
sad: 74.8330%
neutral: 60.8451%
angry: 63.6620%
excited: 80.8989%
frustrated: 65.8537%

总体指标:
总体准确率: 66.4202%
加权准确率 (W_ACC, 按测试集分布): 66.4202%
总体F1分数 (不加权宏平均): 64.4872%
手动计算宏平均F1: 64.4872%
加权F1 (W_F1, 按测试集分布): 66.3461%
============================================================
epoch: 8, train_loss: 10.6043, train_acc: 64.8537, train_fscore: 64.4114, test_loss: 10.9892, test_acc: 66.4202, test_fscore: 66.3461, time: 23.66 sec
epoch: 9, train_loss: 10.5362, train_acc: 66.747, train_fscore: 65.9905, test_loss: 10.7911, test_acc: 65.5576, test_fscore: 66.1858, time: 22.34 sec

*** 新的最优F1分数: 66.9744% (Epoch 10) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 56.9444% (144 样本)
sad: 74.2857% (245 样本)
neutral: 58.8542% (384 样本)
angry: 75.2941% (170 样本)
excited: 74.5819% (299 样本)
frustrated: 62.7297% (381 样本)

每个类别的F1分数:
happy: 46.8571%
sad: 76.6316%
neutral: 64.2959%
angry: 66.1499%
excited: 76.8966%
frustrated: 63.6485%

总体指标:
总体准确率: 66.5434%
加权准确率 (W_ACC, 按测试集分布): 66.5434%
总体F1分数 (不加权宏平均): 65.7466%
手动计算宏平均F1: 65.7466%
加权F1 (W_F1, 按测试集分布): 66.9744%
============================================================
epoch: 10, train_loss: 10.389, train_acc: 67.642, train_fscore: 67.2999, test_loss: 10.6236, test_acc: 66.5434, test_fscore: 66.9744, time: 21.97 sec
----------best F-Score: 66.9744
              precision    recall  f1-score   support

           0     0.3981    0.5694    0.4686       144
           1     0.7913    0.7429    0.7663       245
           2     0.7085    0.5885    0.6430       384
           3     0.5899    0.7529    0.6615       170
           4     0.7936    0.7458    0.7690       299
           5     0.6459    0.6273    0.6365       381

    accuracy                         0.6654      1623
   macro avg     0.6545    0.6712    0.6575      1623
weighted avg     0.6820    0.6654    0.6697      1623

[[ 82   6   6   2  43   5]
 [  6 182  25   2   0  30]
 [ 49  22 226  22   9  56]
 [  0   0   3 128   0  39]
 [ 67   0   8   0 223   1]
 [  2  20  51  63   6 239]]
epoch: 11, train_loss: 10.2227, train_acc: 67.9002, train_fscore: 67.5909, test_loss: 10.7299, test_acc: 66.2354, test_fscore: 66.5406, time: 22.14 sec
epoch: 12, train_loss: 10.1558, train_acc: 68.9157, train_fscore: 68.4001, test_loss: 10.655, test_acc: 64.2637, test_fscore: 64.7601, time: 21.84 sec
epoch: 13, train_loss: 10.0697, train_acc: 67.642, train_fscore: 67.1045, test_loss: 10.573, test_acc: 65.4344, test_fscore: 66.155, time: 21.57 sec

*** 新的最优F1分数: 67.7946% (Epoch 14) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 60.4167% (144 样本)
sad: 79.5918% (245 样本)
neutral: 57.8125% (384 样本)
angry: 74.1176% (170 样本)
excited: 78.2609% (299 样本)
frustrated: 61.4173% (381 样本)

每个类别的F1分数:
happy: 50.7289%
sad: 75.0000%
neutral: 64.9123%
angry: 66.1417%
excited: 78.6555%
frustrated: 64.7303%

总体指标:
总体准确率: 67.6525%
加权准确率 (W_ACC, 按测试集分布): 67.6525%
总体F1分数 (不加权宏平均): 66.6948%
手动计算宏平均F1: 66.6948%
加权F1 (W_F1, 按测试集分布): 67.7946%
============================================================
epoch: 14, train_loss: 9.9797, train_acc: 71.8761, train_fscore: 71.7978, test_loss: 10.3056, test_acc: 67.6525, test_fscore: 67.7946, time: 21.22 sec
epoch: 15, train_loss: 9.8933, train_acc: 70.4131, train_fscore: 69.8959, test_loss: 10.2995, test_acc: 66.8515, test_fscore: 67.5363, time: 20.73 sec
epoch: 16, train_loss: 9.7376, train_acc: 71.704, train_fscore: 71.4969, test_loss: 10.1766, test_acc: 66.6667, test_fscore: 67.1243, time: 20.91 sec

*** 新的最优F1分数: 67.9823% (Epoch 17) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 60.4167% (144 样本)
sad: 71.4286% (245 样本)
neutral: 57.0312% (384 样本)
angry: 80.5882% (170 样本)
excited: 79.5987% (299 样本)
frustrated: 64.0420% (381 样本)

每个类别的F1分数:
happy: 52.5680%
sad: 76.0870%
neutral: 64.7929%
angry: 66.8293%
excited: 79.0698%
frustrated: 63.6245%

总体指标:
总体准确率: 67.7757%
加权准确率 (W_ACC, 按测试集分布): 67.7757%
总体F1分数 (不加权宏平均): 67.1619%
手动计算宏平均F1: 67.1619%
加权F1 (W_F1, 按测试集分布): 67.9823%
============================================================
epoch: 17, train_loss: 9.6373, train_acc: 70.9294, train_fscore: 70.4213, test_loss: 10.0971, test_acc: 67.7757, test_fscore: 67.9823, time: 22.1 sec
epoch: 18, train_loss: 9.5711, train_acc: 72.8571, train_fscore: 72.6182, test_loss: 10.0858, test_acc: 67.406, test_fscore: 67.8464, time: 22.08 sec

*** 新的最优F1分数: 69.1432% (Epoch 19) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 66.6667% (144 样本)
sad: 68.5714% (245 样本)
neutral: 67.9688% (384 样本)
angry: 76.4706% (170 样本)
excited: 73.5786% (299 样本)
frustrated: 62.4672% (381 样本)

每个类别的F1分数:
happy: 51.7520%
sad: 74.8330%
neutral: 68.6842%
angry: 68.9655%
excited: 77.8761%
frustrated: 65.7459%

总体指标:
总体准确率: 68.5767%
加权准确率 (W_ACC, 按测试集分布): 68.5767%
总体F1分数 (不加权宏平均): 67.9761%
手动计算宏平均F1: 67.9761%
加权F1 (W_F1, 按测试集分布): 69.1432%
============================================================
epoch: 19, train_loss: 9.4103, train_acc: 72.5645, train_fscore: 72.1196, test_loss: 10.0669, test_acc: 68.5767, test_fscore: 69.1432, time: 21.91 sec

*** 新的最优F1分数: 69.3660% (Epoch 20) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 72.2222% (144 样本)
sad: 77.1429% (245 样本)
neutral: 63.0208% (384 样本)
angry: 75.8824% (170 样本)
excited: 70.2341% (299 样本)
frustrated: 63.7795% (381 样本)

每个类别的F1分数:
happy: 54.1667%
sad: 78.4232%
neutral: 67.4095%
angry: 68.4350%
excited: 76.7824%
frustrated: 65.8537%

总体指标:
总体准确率: 68.8232%
加权准确率 (W_ACC, 按测试集分布): 68.8232%
总体F1分数 (不加权宏平均): 68.5117%
手动计算宏平均F1: 68.5117%
加权F1 (W_F1, 按测试集分布): 69.3660%
============================================================
epoch: 20, train_loss: 9.3442, train_acc: 74.062, train_fscore: 73.8459, test_loss: 10.0001, test_acc: 68.8232, test_fscore: 69.366, time: 21.81 sec
----------best F-Score: 69.366
              precision    recall  f1-score   support

           0     0.4333    0.7222    0.5417       144
           1     0.7975    0.7714    0.7842       245
           2     0.7246    0.6302    0.6741       384
           3     0.6232    0.7588    0.6844       170
           4     0.8468    0.7023    0.7678       299
           5     0.6807    0.6378    0.6585       381

    accuracy                         0.6882      1623
   macro avg     0.6843    0.7038    0.6851      1623
weighted avg     0.7113    0.6882    0.6937      1623

[[104   4   3   2  27   4]
 [  5 189  23   2   0  26]
 [ 51  23 242  20   3  45]
 [  0   0   3 129   0  38]
 [ 76   0  12   0 210   1]
 [  4  21  51  54   8 243]]
epoch: 21, train_loss: 9.2695, train_acc: 74.148, train_fscore: 73.9051, test_loss: 10.0485, test_acc: 68.0838, test_fscore: 68.6066, time: 21.58 sec
epoch: 22, train_loss: 9.2014, train_acc: 74.5955, train_fscore: 74.4007, test_loss: 9.987, test_acc: 68.5151, test_fscore: 69.0164, time: 22.24 sec
epoch: 23, train_loss: 9.1072, train_acc: 75.4733, train_fscore: 75.2498, test_loss: 9.9682, test_acc: 67.7141, test_fscore: 68.4817, time: 22.22 sec
epoch: 24, train_loss: 9.0586, train_acc: 75.284, train_fscore: 75.0419, test_loss: 9.9153, test_acc: 68.5767, test_fscore: 69.1346, time: 21.73 sec
epoch: 25, train_loss: 9.0013, train_acc: 75.4217, train_fscore: 75.3018, test_loss: 9.7784, test_acc: 68.1454, test_fscore: 68.4707, time: 21.88 sec

*** 新的最优F1分数: 69.4543% (Epoch 26) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.8333% (144 样本)
sad: 78.3673% (245 样本)
neutral: 65.3646% (384 样本)
angry: 72.3529% (170 样本)
excited: 70.5686% (299 样本)
frustrated: 62.7297% (381 样本)

每个类别的F1分数:
happy: 53.6842%
sad: 80.0000%
neutral: 69.0509%
angry: 65.7754%
excited: 76.3110%
frustrated: 65.3005%

总体指标:
总体准确率: 68.8848%
加权准确率 (W_ACC, 按测试集分布): 68.8848%
总体F1分数 (不加权宏平均): 68.3537%
手动计算宏平均F1: 68.3537%
加权F1 (W_F1, 按测试集分布): 69.4543%
============================================================
epoch: 26, train_loss: 8.9334, train_acc: 76.3167, train_fscore: 76.1848, test_loss: 9.6916, test_acc: 68.8848, test_fscore: 69.4543, time: 21.72 sec

*** 新的最优F1分数: 69.6298% (Epoch 27) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.7500% (144 样本)
sad: 72.6531% (245 样本)
neutral: 71.0938% (384 样本)
angry: 70.5882% (170 样本)
excited: 69.8997% (299 样本)
frustrated: 63.5171% (381 样本)

每个类别的F1分数:
happy: 53.9510%
sad: 78.7611%
neutral: 69.8210%
angry: 66.6667%
excited: 75.7246%
frustrated: 66.0300%

总体指标:
总体准确率: 69.0696%
加权准确率 (W_ACC, 按测试集分布): 69.0696%
总体F1分数 (不加权宏平均): 68.4924%
手动计算宏平均F1: 68.4924%
加权F1 (W_F1, 按测试集分布): 69.6298%
============================================================
epoch: 27, train_loss: 8.7006, train_acc: 76.3683, train_fscore: 76.142, test_loss: 9.3599, test_acc: 69.0696, test_fscore: 69.6298, time: 21.84 sec
epoch: 28, train_loss: 8.4365, train_acc: 77.2633, train_fscore: 77.1879, test_loss: 8.9173, test_acc: 67.5909, test_fscore: 68.2492, time: 22.13 sec

*** 新的最优F1分数: 69.9079% (Epoch 29) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 75.0000% (144 样本)
sad: 75.1020% (245 样本)
neutral: 70.0521% (384 样本)
angry: 70.0000% (170 样本)
excited: 66.8896% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 56.9921%
sad: 79.6537%
neutral: 69.3299%
angry: 67.2316%
excited: 74.7664%
frustrated: 66.4865%

总体指标:
总体准确率: 69.3777%
加权准确率 (W_ACC, 按测试集分布): 69.3777%
总体F1分数 (不加权宏平均): 69.0767%
手动计算宏平均F1: 69.0767%
加权F1 (W_F1, 按测试集分布): 69.9079%
============================================================
epoch: 29, train_loss: 8.1149, train_acc: 76.4372, train_fscore: 76.2594, test_loss: 8.1077, test_acc: 69.3777, test_fscore: 69.9079, time: 22.2 sec

*** 新的最优F1分数: 70.4965% (Epoch 30) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.1944% (144 样本)
sad: 76.3265% (245 样本)
neutral: 67.1875% (384 样本)
angry: 75.2941% (170 样本)
excited: 77.9264% (299 样本)
frustrated: 63.7795% (381 样本)

每个类别的F1分数:
happy: 56.0000%
sad: 79.2373%
neutral: 70.3956%
angry: 67.1916%
excited: 79.2517%
frustrated: 65.0602%

总体指标:
总体准确率: 70.2403%
加权准确率 (W_ACC, 按测试集分布): 70.2403%
总体F1分数 (不加权宏平均): 69.5227%
手动计算宏平均F1: 69.5227%
加权F1 (W_F1, 按测试集分布): 70.4965%
============================================================
epoch: 30, train_loss: 7.7497, train_acc: 77.7797, train_fscore: 77.7116, test_loss: 7.7996, test_acc: 70.2403, test_fscore: 70.4965, time: 22.2 sec
----------best F-Score: 70.4965
              precision    recall  f1-score   support

           0     0.5028    0.6319    0.5600       144
           1     0.8238    0.7633    0.7924       245
           2     0.7393    0.6719    0.7040       384
           3     0.6066    0.7529    0.6719       170
           4     0.8062    0.7793    0.7925       299
           5     0.6639    0.6378    0.6506       381

    accuracy                         0.7024      1623
   macro avg     0.6904    0.7062    0.6952      1623
weighted avg     0.7118    0.7024    0.7050      1623

[[ 91   6   4   0  39   4]
 [  3 187  19   3   0  33]
 [ 37  18 258  16   9  46]
 [  0   0   3 128   0  39]
 [ 47   2  16   0 233   1]
 [  3  14  49  64   8 243]]
epoch: 31, train_loss: 7.4775, train_acc: 78.0895, train_fscore: 77.9672, test_loss: 7.4999, test_acc: 69.008, test_fscore: 69.5758, time: 21.99 sec
epoch: 32, train_loss: 7.2006, train_acc: 78.8296, train_fscore: 78.6998, test_loss: 7.2039, test_acc: 67.8373, test_fscore: 68.3836, time: 21.57 sec
epoch: 33, train_loss: 6.9671, train_acc: 78.296, train_fscore: 78.1798, test_loss: 7.0309, test_acc: 67.9606, test_fscore: 68.5443, time: 21.77 sec

*** 新的最优F1分数: 70.9122% (Epoch 34) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.1389% (144 样本)
sad: 78.3673% (245 样本)
neutral: 69.7917% (384 样本)
angry: 72.9412% (170 样本)
excited: 73.2441% (299 样本)
frustrated: 63.2546% (381 样本)

每个类别的F1分数:
happy: 56.9014%
sad: 80.5031%
neutral: 71.3715%
angry: 67.7596%
excited: 77.1127%
frustrated: 66.1180%

总体指标:
总体准确率: 70.5484%
加权准确率 (W_ACC, 按测试集分布): 70.5484%
总体F1分数 (不加权宏平均): 69.9610%
手动计算宏平均F1: 69.9610%
加权F1 (W_F1, 按测试集分布): 70.9122%
============================================================
epoch: 34, train_loss: 6.8512, train_acc: 78.6747, train_fscore: 78.5952, test_loss: 6.7447, test_acc: 70.5484, test_fscore: 70.9122, time: 21.58 sec
epoch: 35, train_loss: 6.6708, train_acc: 79.2427, train_fscore: 79.1838, test_loss: 6.6372, test_acc: 69.6858, test_fscore: 70.0747, time: 21.62 sec
epoch: 36, train_loss: 6.5679, train_acc: 79.432, train_fscore: 79.3372, test_loss: 6.6392, test_acc: 67.3444, test_fscore: 67.8195, time: 21.61 sec
epoch: 37, train_loss: 6.5489, train_acc: 79.4836, train_fscore: 79.3755, test_loss: 6.4812, test_acc: 69.1312, test_fscore: 69.6378, time: 21.6 sec
epoch: 38, train_loss: 6.4969, train_acc: 79.4664, train_fscore: 79.4097, test_loss: 6.4805, test_acc: 70.0555, test_fscore: 70.3254, time: 22.22 sec
epoch: 39, train_loss: 6.293, train_acc: 79.7935, train_fscore: 79.7616, test_loss: 6.2907, test_acc: 68.5767, test_fscore: 68.9608, time: 21.76 sec
epoch: 40, train_loss: 6.3176, train_acc: 79.7935, train_fscore: 79.594, test_loss: 6.4167, test_acc: 69.0696, test_fscore: 69.4143, time: 21.78 sec
----------best F-Score: 70.9122
              precision    recall  f1-score   support

           0     0.4444    0.7778    0.5657       144
           1     0.7927    0.7959    0.7943       245
           2     0.7173    0.7135    0.7154       384
           3     0.6158    0.7353    0.6702       170
           4     0.8443    0.5987    0.7006       299
           5     0.6951    0.5984    0.6432       381

    accuracy                         0.6858      1623
   macro avg     0.6849    0.7033    0.6816      1623
weighted avg     0.7120    0.6858    0.6896      1623

[[112   6   6   0  19   1]
 [  3 195  19   3   0  25]
 [ 36  25 274  13   5  31]
 [  0   0   3 125   0  42]
 [ 97   2  20   0 179   1]
 [  4  18  60  62   9 228]]
epoch: 41, train_loss: 6.1506, train_acc: 80.327, train_fscore: 80.2597, test_loss: 6.2631, test_acc: 68.3919, test_fscore: 69.0392, time: 22.07 sec
epoch: 42, train_loss: 6.0805, train_acc: 80.9294, train_fscore: 80.8478, test_loss: 6.3676, test_acc: 70.4868, test_fscore: 70.8714, time: 21.76 sec
epoch: 43, train_loss: 6.0305, train_acc: 81.1876, train_fscore: 81.1356, test_loss: 6.2306, test_acc: 70.4251, test_fscore: 70.8344, time: 21.71 sec
epoch: 44, train_loss: 5.9772, train_acc: 81.0499, train_fscore: 80.9057, test_loss: 6.1513, test_acc: 69.3777, test_fscore: 69.7597, time: 21.52 sec

*** 新的最优F1分数: 71.7650% (Epoch 45) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 78.4722% (144 样本)
sad: 80.8163% (245 样本)
neutral: 73.9583% (384 样本)
angry: 71.7647% (170 样本)
excited: 64.8829% (299 样本)
frustrated: 65.0919% (381 样本)

每个类别的F1分数:
happy: 60.2667%
sad: 83.3684%
neutral: 72.9140%
angry: 69.3182%
excited: 72.9323%
frustrated: 67.6671%

总体指标:
总体准确率: 71.4110%
加权准确率 (W_ACC, 按测试集分布): 71.4110%
总体F1分数 (不加权宏平均): 71.0778%
手动计算宏平均F1: 71.0778%
加权F1 (W_F1, 按测试集分布): 71.7650%
============================================================
epoch: 45, train_loss: 5.9308, train_acc: 81.6523, train_fscore: 81.5904, test_loss: 5.9378, test_acc: 71.411, test_fscore: 71.765, time: 21.13 sec
epoch: 46, train_loss: 5.91, train_acc: 81.5491, train_fscore: 81.4478, test_loss: 6.0563, test_acc: 70.0555, test_fscore: 70.3258, time: 21.08 sec
epoch: 47, train_loss: 5.8535, train_acc: 82.1687, train_fscore: 82.1034, test_loss: 6.0224, test_acc: 70.1171, test_fscore: 70.5819, time: 20.65 sec
epoch: 48, train_loss: 5.7961, train_acc: 82.3064, train_fscore: 82.2555, test_loss: 5.939, test_acc: 69.5625, test_fscore: 69.94, time: 21.67 sec
epoch: 49, train_loss: 5.7565, train_acc: 82.0138, train_fscore: 81.8706, test_loss: 6.1204, test_acc: 68.6383, test_fscore: 68.738, time: 20.75 sec
epoch: 50, train_loss: 5.6815, train_acc: 82.2719, train_fscore: 82.2087, test_loss: 6.0126, test_acc: 69.1312, test_fscore: 69.2957, time: 22.37 sec
----------best F-Score: 71.765
              precision    recall  f1-score   support

           0     0.4892    0.7847    0.6027       144
           1     0.8609    0.8082    0.8337       245
           2     0.7190    0.7396    0.7291       384
           3     0.6703    0.7176    0.6932       170
           4     0.8326    0.6488    0.7293       299
           5     0.7045    0.6509    0.6767       381

    accuracy                         0.7141      1623
   macro avg     0.7128    0.7250    0.7108      1623
weighted avg     0.7325    0.7141    0.7177      1623

[[113   1   4   0  25   1]
 [  4 198  16   2   0  25]
 [ 34  19 284   8   6  33]
 [  0   0   4 122   0  44]
 [ 76   1  27   0 194   1]
 [  4  11  60  50   8 248]]
epoch: 51, train_loss: 5.6633, train_acc: 82.7194, train_fscore: 82.6097, test_loss: 6.0539, test_acc: 69.5625, test_fscore: 69.9467, time: 21.03 sec
epoch: 52, train_loss: 5.5946, train_acc: 82.7711, train_fscore: 82.6835, test_loss: 5.9026, test_acc: 70.61, test_fscore: 70.9234, time: 20.56 sec
epoch: 53, train_loss: 5.588, train_acc: 82.9432, train_fscore: 82.8442, test_loss: 6.036, test_acc: 69.1312, test_fscore: 69.294, time: 20.99 sec
epoch: 54, train_loss: 5.4718, train_acc: 83.4251, train_fscore: 83.3495, test_loss: 5.8808, test_acc: 70.7332, test_fscore: 70.9998, time: 21.23 sec
epoch: 55, train_loss: 5.4944, train_acc: 83.6317, train_fscore: 83.5529, test_loss: 5.9451, test_acc: 70.3635, test_fscore: 70.7634, time: 21.11 sec
epoch: 56, train_loss: 5.4937, train_acc: 83.5628, train_fscore: 83.4973, test_loss: 6.0131, test_acc: 69.7474, test_fscore: 70.054, time: 21.38 sec
epoch: 57, train_loss: 5.3864, train_acc: 83.7349, train_fscore: 83.6417, test_loss: 5.9705, test_acc: 69.9938, test_fscore: 70.1327, time: 20.9 sec
epoch: 58, train_loss: 5.3586, train_acc: 84.5095, train_fscore: 84.3956, test_loss: 6.014, test_acc: 69.8706, test_fscore: 69.9499, time: 20.94 sec
epoch: 59, train_loss: 5.3982, train_acc: 84.0103, train_fscore: 83.9043, test_loss: 5.9033, test_acc: 70.4251, test_fscore: 70.9076, time: 21.04 sec
epoch: 60, train_loss: 5.2773, train_acc: 85.0258, train_fscore: 84.9453, test_loss: 5.9454, test_acc: 68.8232, test_fscore: 69.1507, time: 21.53 sec
----------best F-Score: 71.765
              precision    recall  f1-score   support

           0     0.5098    0.7222    0.5977       144
           1     0.8093    0.7796    0.7942       245
           2     0.7242    0.7318    0.7280       384
           3     0.6345    0.7353    0.6812       170
           4     0.8204    0.6722    0.7390       299
           5     0.6969    0.6457    0.6703       381

    accuracy                         0.7073      1623
   macro avg     0.6992    0.7145    0.7017      1623
weighted avg     0.7200    0.7073    0.7100      1623

[[104   5   5   0  29   1]
 [  3 191  20   2   0  29]
 [ 30  23 281  10   6  34]
 [  0   0   3 125   0  42]
 [ 66   3  28   0 201   1]
 [  1  14  51  60   9 246]]
epoch: 61, train_loss: 5.2864, train_acc: 84.148, train_fscore: 84.0401, test_loss: 5.9112, test_acc: 69.809, test_fscore: 70.0056, time: 21.12 sec
epoch: 62, train_loss: 5.2921, train_acc: 84.475, train_fscore: 84.4031, test_loss: 5.8193, test_acc: 70.2403, test_fscore: 70.3326, time: 21.5 sec
epoch: 63, train_loss: 5.2184, train_acc: 85.1635, train_fscore: 85.0736, test_loss: 5.9041, test_acc: 69.9322, test_fscore: 70.2063, time: 21.25 sec
epoch: 64, train_loss: 5.2443, train_acc: 84.5955, train_fscore: 84.4919, test_loss: 5.8598, test_acc: 70.0555, test_fscore: 70.3621, time: 21.37 sec
epoch: 65, train_loss: 5.1937, train_acc: 85.1979, train_fscore: 85.1109, test_loss: 5.9364, test_acc: 70.1787, test_fscore: 70.2948, time: 22.43 sec
epoch: 66, train_loss: 5.094, train_acc: 85.6627, train_fscore: 85.5811, test_loss: 5.8561, test_acc: 70.6716, test_fscore: 70.8427, time: 21.53 sec
epoch: 67, train_loss: 5.0561, train_acc: 85.2668, train_fscore: 85.1958, test_loss: 5.8375, test_acc: 69.5625, test_fscore: 69.7679, time: 21.86 sec
epoch: 68, train_loss: 5.0608, train_acc: 85.043, train_fscore: 84.949, test_loss: 5.9193, test_acc: 70.0555, test_fscore: 70.1571, time: 21.53 sec
epoch: 69, train_loss: 5.052, train_acc: 85.2496, train_fscore: 85.161, test_loss: 5.8755, test_acc: 70.9181, test_fscore: 71.1119, time: 21.59 sec
epoch: 70, train_loss: 5.023, train_acc: 85.3528, train_fscore: 85.2641, test_loss: 6.0895, test_acc: 68.7616, test_fscore: 68.6524, time: 21.81 sec
----------best F-Score: 71.765
              precision    recall  f1-score   support

           0     0.5045    0.7778    0.6120       144
           1     0.8333    0.7755    0.8034       245
           2     0.6875    0.8021    0.7404       384
           3     0.6508    0.7235    0.6852       170
           4     0.8326    0.5987    0.6965       299
           5     0.7103    0.5984    0.6496       381

    accuracy                         0.7024      1623
   macro avg     0.7032    0.7127    0.6978      1623
weighted avg     0.7215    0.7024    0.7033      1623

[[112   1   4   0  26   1]
 [  3 190  24   2   0  26]
 [ 24  18 308   5   4  25]
 [  0   1   6 123   0  40]
 [ 81   2  36   0 179   1]
 [  2  16  70  59   6 228]]
epoch: 71, train_loss: 4.9848, train_acc: 85.284, train_fscore: 85.166, test_loss: 6.0017, test_acc: 71.1029, test_fscore: 71.2955, time: 21.17 sec
epoch: 72, train_loss: 4.9323, train_acc: 86.3339, train_fscore: 86.2581, test_loss: 5.7568, test_acc: 70.6716, test_fscore: 70.8488, time: 21.69 sec
epoch: 73, train_loss: 4.9509, train_acc: 86.1102, train_fscore: 86.0088, test_loss: 6.0077, test_acc: 70.1787, test_fscore: 70.3622, time: 21.74 sec
epoch: 74, train_loss: 4.923, train_acc: 86.2823, train_fscore: 86.2012, test_loss: 5.9496, test_acc: 70.7332, test_fscore: 70.6398, time: 21.25 sec
epoch: 75, train_loss: 4.9283, train_acc: 86.8158, train_fscore: 86.7205, test_loss: 5.934, test_acc: 69.4393, test_fscore: 69.7084, time: 20.78 sec
epoch: 76, train_loss: 4.7945, train_acc: 87.4699, train_fscore: 87.3884, test_loss: 5.9954, test_acc: 69.5009, test_fscore: 69.5485, time: 21.28 sec
epoch: 77, train_loss: 4.783, train_acc: 86.833, train_fscore: 86.7449, test_loss: 5.9281, test_acc: 70.9797, test_fscore: 70.9564, time: 21.17 sec
epoch: 78, train_loss: 4.8312, train_acc: 87.0568, train_fscore: 86.9985, test_loss: 5.9159, test_acc: 71.2261, test_fscore: 71.1561, time: 21.43 sec

*** 新的最优F1分数: 72.1605% (Epoch 79) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 74.3056% (144 样本)
sad: 71.8367% (245 样本)
neutral: 80.4688% (384 样本)
angry: 71.7647% (170 样本)
excited: 68.2274% (299 样本)
frustrated: 65.6168% (381 样本)

每个类别的F1分数:
happy: 63.6905%
sad: 79.4582%
neutral: 75.6426%
angry: 68.5393%
excited: 74.0472%
frustrated: 67.2948%

总体指标:
总体准确率: 71.9655%
加权准确率 (W_ACC, 按测试集分布): 71.9655%
总体F1分数 (不加权宏平均): 71.4454%
手动计算宏平均F1: 71.4454%
加权F1 (W_F1, 按测试集分布): 72.1605%
============================================================
epoch: 79, train_loss: 4.8132, train_acc: 87.1601, train_fscore: 87.0096, test_loss: 5.8539, test_acc: 71.9655, test_fscore: 72.1605, time: 21.53 sec
epoch: 80, train_loss: 4.7306, train_acc: 87.4355, train_fscore: 87.3881, test_loss: 5.9417, test_acc: 70.7332, test_fscore: 70.7769, time: 21.23 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 81, train_loss: 4.7489, train_acc: 86.988, train_fscore: 86.8717, test_loss: 6.1602, test_acc: 70.4251, test_fscore: 70.4486, time: 20.79 sec
epoch: 82, train_loss: 4.7772, train_acc: 87.2633, train_fscore: 87.1785, test_loss: 6.1217, test_acc: 69.2545, test_fscore: 69.3405, time: 21.07 sec
epoch: 83, train_loss: 4.6621, train_acc: 87.315, train_fscore: 87.2404, test_loss: 5.8966, test_acc: 70.1787, test_fscore: 70.0854, time: 21.72 sec
epoch: 84, train_loss: 4.6801, train_acc: 87.4527, train_fscore: 87.3669, test_loss: 5.9813, test_acc: 70.9797, test_fscore: 70.9522, time: 21.24 sec
epoch: 85, train_loss: 4.5968, train_acc: 88.0379, train_fscore: 87.9481, test_loss: 6.0617, test_acc: 70.1171, test_fscore: 70.2289, time: 20.95 sec
epoch: 86, train_loss: 4.6166, train_acc: 87.7969, train_fscore: 87.7316, test_loss: 5.9582, test_acc: 70.3019, test_fscore: 70.397, time: 21.64 sec
epoch: 87, train_loss: 4.5886, train_acc: 88.0379, train_fscore: 87.964, test_loss: 6.1001, test_acc: 70.0555, test_fscore: 70.0662, time: 21.85 sec
epoch: 88, train_loss: 4.5594, train_acc: 88.1756, train_fscore: 88.0999, test_loss: 6.0703, test_acc: 71.2877, test_fscore: 71.3668, time: 21.71 sec
epoch: 89, train_loss: 4.5265, train_acc: 88.5542, train_fscore: 88.4951, test_loss: 6.2213, test_acc: 69.9322, test_fscore: 69.8292, time: 21.67 sec
epoch: 90, train_loss: 4.533, train_acc: 88.5198, train_fscore: 88.4324, test_loss: 6.1703, test_acc: 69.3777, test_fscore: 69.3083, time: 21.73 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 91, train_loss: 4.5379, train_acc: 88.8812, train_fscore: 88.7968, test_loss: 5.9898, test_acc: 72.0271, test_fscore: 72.0913, time: 21.62 sec
epoch: 92, train_loss: 4.4829, train_acc: 88.6231, train_fscore: 88.5602, test_loss: 6.1565, test_acc: 70.3635, test_fscore: 70.2541, time: 23.95 sec
epoch: 93, train_loss: 4.4378, train_acc: 89.1738, train_fscore: 89.0908, test_loss: 6.0249, test_acc: 71.2261, test_fscore: 71.322, time: 21.99 sec
epoch: 94, train_loss: 4.3879, train_acc: 89.2255, train_fscore: 89.1596, test_loss: 6.1412, test_acc: 70.1787, test_fscore: 70.2742, time: 21.38 sec
epoch: 95, train_loss: 4.4498, train_acc: 89.0189, train_fscore: 88.9453, test_loss: 6.2745, test_acc: 69.8706, test_fscore: 69.7576, time: 21.65 sec
epoch: 96, train_loss: 4.3996, train_acc: 89.673, train_fscore: 89.6093, test_loss: 6.2607, test_acc: 69.8706, test_fscore: 69.781, time: 21.52 sec
epoch: 97, train_loss: 4.4025, train_acc: 90.1893, train_fscore: 90.129, test_loss: 6.1412, test_acc: 71.3494, test_fscore: 71.3837, time: 21.63 sec
epoch: 98, train_loss: 4.3552, train_acc: 89.5181, train_fscore: 89.4563, test_loss: 6.2827, test_acc: 70.0555, test_fscore: 70.0501, time: 21.63 sec
epoch: 99, train_loss: 4.3677, train_acc: 89.6902, train_fscore: 89.6354, test_loss: 6.1977, test_acc: 69.9938, test_fscore: 69.9017, time: 21.79 sec
epoch: 100, train_loss: 4.3707, train_acc: 88.864, train_fscore: 88.7405, test_loss: 6.3765, test_acc: 69.2545, test_fscore: 69.1894, time: 22.12 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 101, train_loss: 4.3511, train_acc: 90.1205, train_fscore: 90.0569, test_loss: 6.0988, test_acc: 70.5484, test_fscore: 70.4849, time: 21.39 sec
epoch: 102, train_loss: 4.3239, train_acc: 90.1893, train_fscore: 90.1168, test_loss: 6.1589, test_acc: 70.7332, test_fscore: 70.7433, time: 21.05 sec
epoch: 103, train_loss: 4.2805, train_acc: 90.1377, train_fscore: 90.0693, test_loss: 6.2546, test_acc: 70.7948, test_fscore: 70.7405, time: 21.32 sec
epoch: 104, train_loss: 4.2894, train_acc: 90.4303, train_fscore: 90.3814, test_loss: 6.0753, test_acc: 71.1645, test_fscore: 71.1747, time: 20.58 sec
epoch: 105, train_loss: 4.2054, train_acc: 90.654, train_fscore: 90.5967, test_loss: 6.111, test_acc: 71.719, test_fscore: 71.7939, time: 21.66 sec
epoch: 106, train_loss: 4.266, train_acc: 90.3098, train_fscore: 90.2392, test_loss: 6.363, test_acc: 70.2403, test_fscore: 70.2347, time: 21.88 sec
epoch: 107, train_loss: 4.2652, train_acc: 90.5164, train_fscore: 90.4756, test_loss: 6.4109, test_acc: 70.1171, test_fscore: 69.9408, time: 22.28 sec
epoch: 108, train_loss: 4.2297, train_acc: 90.1549, train_fscore: 90.0715, test_loss: 6.397, test_acc: 70.5484, test_fscore: 70.5965, time: 21.91 sec
epoch: 109, train_loss: 4.1625, train_acc: 91.0155, train_fscore: 90.9471, test_loss: 6.2425, test_acc: 70.7332, test_fscore: 70.7466, time: 21.95 sec
epoch: 110, train_loss: 4.2179, train_acc: 90.4475, train_fscore: 90.3961, test_loss: 6.4167, test_acc: 70.0555, test_fscore: 70.0379, time: 21.8 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 111, train_loss: 4.1981, train_acc: 90.0516, train_fscore: 89.9996, test_loss: 6.4207, test_acc: 70.7332, test_fscore: 70.707, time: 20.81 sec
epoch: 112, train_loss: 4.1662, train_acc: 91.2909, train_fscore: 91.2337, test_loss: 6.3607, test_acc: 70.61, test_fscore: 70.6946, time: 21.88 sec
epoch: 113, train_loss: 4.1948, train_acc: 90.9983, train_fscore: 90.9403, test_loss: 6.383, test_acc: 71.5342, test_fscore: 71.6108, time: 21.9 sec
epoch: 114, train_loss: 4.1545, train_acc: 90.9639, train_fscore: 90.9122, test_loss: 6.3788, test_acc: 71.6574, test_fscore: 71.7561, time: 21.53 sec
epoch: 115, train_loss: 4.156, train_acc: 91.136, train_fscore: 91.0913, test_loss: 6.5313, test_acc: 70.1171, test_fscore: 70.1363, time: 21.82 sec
epoch: 116, train_loss: 4.1757, train_acc: 90.9811, train_fscore: 90.9337, test_loss: 6.1843, test_acc: 70.6716, test_fscore: 70.724, time: 21.59 sec
epoch: 117, train_loss: 4.1665, train_acc: 90.895, train_fscore: 90.8246, test_loss: 6.4745, test_acc: 69.8706, test_fscore: 69.8884, time: 21.8 sec
epoch: 118, train_loss: 4.1661, train_acc: 90.568, train_fscore: 90.5071, test_loss: 6.4062, test_acc: 71.719, test_fscore: 71.8743, time: 22.16 sec
epoch: 119, train_loss: 4.0775, train_acc: 91.2565, train_fscore: 91.217, test_loss: 6.3745, test_acc: 70.1787, test_fscore: 70.0953, time: 21.61 sec
epoch: 120, train_loss: 4.0383, train_acc: 92.0826, train_fscore: 92.02, test_loss: 6.3522, test_acc: 69.9938, test_fscore: 70.0894, time: 21.98 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 121, train_loss: 4.0326, train_acc: 91.6007, train_fscore: 91.5364, test_loss: 6.409, test_acc: 70.5484, test_fscore: 70.5651, time: 21.82 sec
epoch: 122, train_loss: 4.0331, train_acc: 91.9621, train_fscore: 91.9085, test_loss: 6.5095, test_acc: 69.5625, test_fscore: 69.4856, time: 21.48 sec
epoch: 123, train_loss: 4.0375, train_acc: 92.117, train_fscore: 92.0572, test_loss: 6.3926, test_acc: 70.4251, test_fscore: 70.4641, time: 21.76 sec
epoch: 124, train_loss: 4.0082, train_acc: 92.5129, train_fscore: 92.4625, test_loss: 6.3761, test_acc: 70.7948, test_fscore: 70.8524, time: 21.72 sec
epoch: 125, train_loss: 3.9251, train_acc: 92.3752, train_fscore: 92.3277, test_loss: 6.495, test_acc: 70.2403, test_fscore: 70.2371, time: 21.87 sec
epoch: 126, train_loss: 3.9848, train_acc: 92.031, train_fscore: 91.9744, test_loss: 6.4478, test_acc: 71.4726, test_fscore: 71.5339, time: 21.72 sec
epoch: 127, train_loss: 3.9316, train_acc: 92.5301, train_fscore: 92.4852, test_loss: 6.563, test_acc: 70.3019, test_fscore: 70.3328, time: 21.77 sec
epoch: 128, train_loss: 3.946, train_acc: 92.3408, train_fscore: 92.2882, test_loss: 6.7078, test_acc: 69.9322, test_fscore: 69.9529, time: 21.8 sec
epoch: 129, train_loss: 3.8734, train_acc: 92.4785, train_fscore: 92.4236, test_loss: 6.5399, test_acc: 71.1029, test_fscore: 71.1634, time: 21.87 sec
epoch: 130, train_loss: 3.8618, train_acc: 92.8227, train_fscore: 92.7971, test_loss: 6.6129, test_acc: 69.6242, test_fscore: 69.6074, time: 21.55 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 131, train_loss: 3.8745, train_acc: 92.3408, train_fscore: 92.283, test_loss: 6.7722, test_acc: 69.6242, test_fscore: 69.6717, time: 21.84 sec
epoch: 132, train_loss: 3.8955, train_acc: 93.0637, train_fscore: 93.0262, test_loss: 6.6355, test_acc: 70.7332, test_fscore: 70.6831, time: 21.72 sec
epoch: 133, train_loss: 3.9252, train_acc: 93.0293, train_fscore: 92.9926, test_loss: 6.736, test_acc: 70.61, test_fscore: 70.6328, time: 23.61 sec
epoch: 134, train_loss: 3.9136, train_acc: 92.8399, train_fscore: 92.7953, test_loss: 6.6036, test_acc: 69.8706, test_fscore: 69.7211, time: 21.65 sec
epoch: 135, train_loss: 3.918, train_acc: 92.6334, train_fscore: 92.5933, test_loss: 6.8949, test_acc: 69.1929, test_fscore: 68.9539, time: 21.76 sec
epoch: 136, train_loss: 3.873, train_acc: 92.6506, train_fscore: 92.5895, test_loss: 6.5538, test_acc: 70.4868, test_fscore: 70.6215, time: 21.95 sec
epoch: 137, train_loss: 3.828, train_acc: 92.4785, train_fscore: 92.4475, test_loss: 6.7684, test_acc: 69.6242, test_fscore: 69.4782, time: 21.68 sec
epoch: 138, train_loss: 3.8539, train_acc: 92.926, train_fscore: 92.891, test_loss: 6.5164, test_acc: 71.4726, test_fscore: 71.5191, time: 21.52 sec
epoch: 139, train_loss: 3.8497, train_acc: 93.0465, train_fscore: 93.0029, test_loss: 6.6006, test_acc: 70.0555, test_fscore: 70.1565, time: 22.02 sec
epoch: 140, train_loss: 3.7311, train_acc: 92.9088, train_fscore: 92.8668, test_loss: 6.9003, test_acc: 69.8706, test_fscore: 69.8695, time: 22.04 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
epoch: 141, train_loss: 3.7609, train_acc: 93.6661, train_fscore: 93.6365, test_loss: 6.6458, test_acc: 69.9938, test_fscore: 70.0222, time: 21.7 sec
epoch: 142, train_loss: 3.7606, train_acc: 93.821, train_fscore: 93.7744, test_loss: 6.7154, test_acc: 70.61, test_fscore: 70.5892, time: 22.49 sec
epoch: 143, train_loss: 3.771, train_acc: 93.6833, train_fscore: 93.655, test_loss: 6.6966, test_acc: 70.3635, test_fscore: 70.3186, time: 21.93 sec
epoch: 144, train_loss: 3.776, train_acc: 93.6489, train_fscore: 93.6111, test_loss: 6.712, test_acc: 70.1171, test_fscore: 70.2197, time: 21.83 sec
epoch: 145, train_loss: 3.7007, train_acc: 93.7005, train_fscore: 93.6584, test_loss: 6.9021, test_acc: 69.809, test_fscore: 69.7419, time: 21.7 sec
epoch: 146, train_loss: 3.7375, train_acc: 93.7177, train_fscore: 93.6888, test_loss: 6.7294, test_acc: 71.2877, test_fscore: 71.3458, time: 21.85 sec
epoch: 147, train_loss: 3.7146, train_acc: 93.8382, train_fscore: 93.8036, test_loss: 6.9126, test_acc: 69.1312, test_fscore: 69.1973, time: 21.74 sec
epoch: 148, train_loss: 3.7027, train_acc: 94.062, train_fscore: 94.0223, test_loss: 6.857, test_acc: 69.5625, test_fscore: 69.4436, time: 21.83 sec
epoch: 149, train_loss: 3.7117, train_acc: 93.5628, train_fscore: 93.5308, test_loss: 7.0409, test_acc: 69.9322, test_fscore: 69.7406, time: 21.76 sec
epoch: 150, train_loss: 3.7142, train_acc: 94.0964, train_fscore: 94.0684, test_loss: 7.1145, test_acc: 68.9464, test_fscore: 68.9845, time: 21.79 sec
----------best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
 [  0   0   5 122   0  43]
 [ 62   0  31   0 204   2]
 [  1   9  54  58   9 250]]
Test performance..
Best F-Score: 72.1605
              precision    recall  f1-score   support

           0     0.5573    0.7431    0.6369       144
           1     0.8889    0.7184    0.7946       245
           2     0.7136    0.8047    0.7564       384
           3     0.6559    0.7176    0.6854       170
           4     0.8095    0.6823    0.7405       299
           5     0.6906    0.6562    0.6729       381

    accuracy                         0.7197      1623
   macro avg     0.7193    0.7204    0.7145      1623
weighted avg     0.7324    0.7197    0.7216      1623

[[107   1   3   0  32   1]
 [  2 176  31   2   0  34]
 [ 20  12 309   4   7  32]
==================================================
DETAILED EVALUATION WITH WEIGHTS
==================================================

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 74.3056% (144 样本)
sad: 71.8367% (245 样本)
neutral: 80.4688% (384 样本)
angry: 71.7647% (170 样本)
excited: 68.2274% (299 样本)
frustrated: 65.6168% (381 样本)

每个类别的F1分数:
happy: 63.6905%
sad: 79.4582%
neutral: 75.6426%
angry: 68.5393%
excited: 74.0472%
frustrated: 67.2948%

总体指标:
总体准确率: 71.9655%
加权准确率 (W_ACC, 按测试集分布): 71.9655%
总体F1分数 (不加权宏平均): 71.4454%
手动计算宏平均F1: 71.4454%
加权F1 (W_F1, 按测试集分布): 72.1605%
============================================================