import re

# 定义要过滤的警告信息的正则模式
warning_pattern = re.compile(r'D:\\dev\\pycharm\\emotion_recognition\\SDT-main\\SDT-main\\.*?:\d+: (UserWarning|RuntimeWarning): .*')

# 读取原始日志文件
with open('错误的设置label.txt', 'r', encoding='utf-8') as file:
    lines = file.readlines()

# 过滤掉匹配警告模式的行
filtered_lines = [line for line in lines if not warning_pattern.match(line)]

# 将过滤后的结果写入新文件
with open('../../loss计算错误version1/filtered_log.txt', 'w', encoding='utf-8') as file:
    file.writelines(filtered_lines)

print("警告信息已从日志文件中删除，并保存到 filtered_log.txt")