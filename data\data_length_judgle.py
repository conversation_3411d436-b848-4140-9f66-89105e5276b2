import pickle
import numpy as np
# 打开pkl文件
with open('iemocap_multimodal_features.pkl', 'rb') as file:
    data = pickle.load(file)
# 检查数据一致性（以Ses02F_script01_3为例）
session_id = "Ses02F_script01_3"

assert len(data[0][session_id]) == len(data[1][session_id]) == \
       len(data[2][session_id]) == len(data[3][session_id])==len(data[4][session_id])\
       == len(data[5][session_id]) == len(data[6][session_id]) == len(data[7][session_id]), \
"数据长度不一致！"

# 样本索引验证
sample_idx = 1
print("样本ID:", data[0][session_id][sample_idx])  # Ses02F_script01_3_F009
print("说话者:", data[1][session_id][sample_idx])  # F
print("情感标签:", data[2][session_id][sample_idx]) # 1 (sad)
print("文本特征:", data[3][session_id][sample_idx]) #1024维度的文本特征 [ 0.29903188 -0.12496942  ... -0.17718671 -1.3023459 2.3025506 ]
print("roberta特征2:",data[4][session_id][sample_idx])
print("roberta特征3:",data[5][session_id][sample_idx])
print("roberta特征4:",data[6][session_id][sample_idx])
print("audio特征:",data[7][session_id][sample_idx])
print("audio特征形状:",data[7][session_id][sample_idx].shape)
print("video特征:",data[9][session_id][sample_idx])
print("video特征形状:",data[9][session_id][0])


print("roberta特征2:",len(data[4][session_id][sample_idx]))
print("roberta特征3:",len(data[5][session_id][sample_idx]))
print("roberta特征4:",len(data[6][session_id][sample_idx]))
print("audio特征:",len(data[7][session_id][sample_idx]))
print("audio特征:",len(data[9]["Ses03F_impro06"][sample_idx]))


print("样本len:", len(data[0]["Ses03F_impro06"]))
print("样本len:", len(data[4]["Ses03F_impro06"]))
print("样本len:", len(data[6]["Ses03F_impro06"]))
print("样本len:", len(data[9]["Ses03F_impro06"]))
print("样本len:", len(data[9][session_id]))
