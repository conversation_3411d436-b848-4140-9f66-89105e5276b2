C:\Users\<USER>\anaconda3\envs\pytorch\python.exe D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py
Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=1, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 1
total parameters: 79687704
training parameters: 79687704
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
x shape: torch.Size([16, 94, 1024])
pos_emb shape: torch.Size([1, 94, 1024])
speaker_emb shape: torch.Size([794, 1024])
Traceback (most recent call last):
  File "D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py", line 257, in <module>
    train_loss, train_acc, _, _,  train_fscore , _  = train_or_eval_model(model, loss_function, kl_loss, train_loader,
                                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py", line 98, in train_or_eval_model
    all_log_prob = model(textf, visuf, acouf, umask, qmask, lengths)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\model.py", line 338, in forward
    t_t_transformer_out = self.t_t(textf, textf, u_mask, spk_embeddings)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\model.py", line 202, in forward
    x_b = self.pos_emb(x_b, speaker_emb)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\pytorch\Lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\model.py", line 137, in forward
    x = x + pos_emb + speaker_emb
        ~~~~~~~~~~~~^~~~~~~~~~~~~
RuntimeError: The size of tensor a (94) must match the size of tensor b (794) at non-singleton dimension 1

进程已结束，退出代码为 1
