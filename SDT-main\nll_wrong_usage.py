#!/usr/bin/env python3
"""
演示NLLLoss错误用法的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

def test_nll_wrong_usage():
    """
    测试NLLLoss的错误用法
    """
    print("="*60)
    print("NLLLoss 错误用法演示")
    print("="*60)
    
    # 3分类任务，2个样本
    logits = torch.tensor([
        [2.0, 1.0, 0.5],   # 样本1
        [0.8, 2.5, 1.2]    # 样本2
    ], dtype=torch.float32)
    
    targets = torch.tensor([0, 1], dtype=torch.long)
    
    print(f"原始logits:\n{logits}")
    print(f"真实标签: {targets}")
    
    # 正确用法
    nll_criterion = nn.NLLLoss()
    log_probs = F.log_softmax(logits, dim=1)
    correct_loss = nll_criterion(log_probs, targets)
    
    print(f"\n✅ 正确用法:")
    print(f"log_softmax后的值:\n{log_probs}")
    print(f"正确的NLL损失: {correct_loss.item():.6f}")
    
    # 错误用法：直接用logits
    print(f"\n❌ 错误用法:")
    print("直接用原始logits (不做log_softmax):")
    wrong_loss = nll_criterion(logits, targets)
    print(f"错误的NLL损失: {wrong_loss.item():.6f}")
    
    # 分析为什么错误
    print(f"\n分析错误原因:")
    print(f"NLLLoss期望输入是log概率 (负数，因为概率<1)")
    print(f"但原始logits通常是正数")
    print(f"样本1选择的值: logits[0,0] = {logits[0,0].item():.3f} (正数!)")
    print(f"样本2选择的值: logits[1,1] = {logits[1,1].item():.3f} (正数!)")
    print(f"NLL = -平均值 = -({logits[0,0].item():.3f} + {logits[1,1].item():.3f})/2 = {wrong_loss.item():.6f}")
    print(f"结果是负数! 这在数学上是错误的")
    
    # 对比正确的计算
    print(f"\n正确计算:")
    print(f"样本1选择的log概率: {log_probs[0,0].item():.6f} (负数)")
    print(f"样本2选择的log概率: {log_probs[1,1].item():.6f} (负数)")
    manual_correct = -(log_probs[0,0] + log_probs[1,1]) / 2
    print(f"NLL = -平均值 = {manual_correct.item():.6f} (正数)")
    
    # 与CrossEntropyLoss对比
    ce_criterion = nn.CrossEntropyLoss()
    ce_loss = ce_criterion(logits, targets)
    print(f"\nCrossEntropyLoss (正确): {ce_loss.item():.6f}")
    print(f"正确NLL损失: {correct_loss.item():.6f}")
    print(f"错误NLL损失: {wrong_loss.item():.6f}")
    
    print(f"\n结论:")
    print(f"1. NLLLoss必须输入log概率，不能直接输入logits")
    print(f"2. 直接输入logits会导致负的损失值")
    print(f"3. 负的损失值在数学上没有意义")
    print(f"4. 使用CrossEntropyLoss可以直接输入logits")

def test_extreme_case():
    """
    测试极端情况
    """
    print(f"\n" + "="*60)
    print("极端情况测试")
    print("="*60)
    
    # 很大的logits值
    large_logits = torch.tensor([
        [10.0, 5.0, 2.0],   # 很大的值
        [8.0, 15.0, 3.0]    # 很大的值
    ], dtype=torch.float32)
    
    targets = torch.tensor([0, 1], dtype=torch.long)
    
    print(f"大的logits值:\n{large_logits}")
    
    nll_criterion = nn.NLLLoss()
    
    # 正确用法
    log_probs = F.log_softmax(large_logits, dim=1)
    correct_loss = nll_criterion(log_probs, targets)
    
    # 错误用法
    wrong_loss = nll_criterion(large_logits, targets)
    
    print(f"\n正确NLL损失: {correct_loss.item():.6f}")
    print(f"错误NLL损失: {wrong_loss.item():.6f}")
    print(f"错误用法导致很大的负值!")
    
    # 很小的logits值
    small_logits = torch.tensor([
        [-2.0, -5.0, -8.0],   # 负值
        [-3.0, -1.0, -6.0]    # 负值
    ], dtype=torch.float32)
    
    print(f"\n小的logits值:\n{small_logits}")
    
    log_probs_small = F.log_softmax(small_logits, dim=1)
    correct_loss_small = nll_criterion(log_probs_small, targets)
    wrong_loss_small = nll_criterion(small_logits, targets)
    
    print(f"正确NLL损失: {correct_loss_small.item():.6f}")
    print(f"错误NLL损失: {wrong_loss_small.item():.6f}")

if __name__ == "__main__":
    test_nll_wrong_usage()
    test_extreme_case()
    
    print(f"\n" + "="*60)
    print("总结:")
    print("1. NLLLoss输入必须是log概率 (用log_softmax)")
    print("2. 直接输入logits会导致错误的负损失值")
    print("3. 如果要直接用logits，请使用CrossEntropyLoss")
    print("4. CrossEntropyLoss = log_softmax + NLLLoss")
    print("="*60)
