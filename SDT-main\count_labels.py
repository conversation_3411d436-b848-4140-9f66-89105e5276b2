#!/usr/bin/env python3
"""
统计训练集中所有标签个数的工具
"""

import pickle
import numpy as np
from collections import Counter
import argparse

def count_iemocap_labels():
    """统计IEMOCAP数据集的标签分布"""
    print("正在加载IEMOCAP数据集...")
    
    try:
        # 加载数据
        with open('../data/iemocap_multimodal_features.pkl', 'rb') as f:
            data = pickle.load(f, encoding='latin1')
        
        videoIDs, videoSpeakers, videoLabels, videoText, roberta2, roberta3, roberta4, \
        videoAudio, videoVisual, videoSentence, trainVid, testVid = data
        
        # 定义类别名称 (根据您提供的映射)
        class_names = {
            0: 'happy',     # hap
            1: 'sad',       # sad
            2: 'neutral',   # neu
            3: 'angry',     # ang
            4: 'excited',   # exc
            5: 'frustrated' # fru
        }
        
        print("\n" + "="*60)
        print("IEMOCAP 数据集标签统计")
        print("="*60)
        
        # 统计训练集标签
        train_labels = []
        for vid in trainVid:
            train_labels.extend(videoLabels[vid])
        
        train_counter = Counter(train_labels)
        total_train = len(train_labels)
        
        print(f"\n训练集标签分布 (总计: {total_train} 个样本):")
        print(f"{'类别':<12} {'标签':<8} {'数量':<8} {'比例':<10}")
        print("-" * 45)
        
        for label in sorted(train_counter.keys()):
            count = train_counter[label]
            percentage = count / total_train * 100
            class_name = class_names.get(label, f'unknown_{label}')
            print(f"{class_name:<12} {label:<8} {count:<8} {percentage:<10.2f}%")
        
        # 统计测试集标签
        test_labels = []
        for vid in testVid:
            test_labels.extend(videoLabels[vid])
        
        test_counter = Counter(test_labels)
        total_test = len(test_labels)
        
        print(f"\n测试集标签分布 (总计: {total_test} 个样本):")
        print(f"{'类别':<12} {'标签':<8} {'数量':<8} {'比例':<10}")
        print("-" * 45)
        
        for label in sorted(test_counter.keys()):
            count = test_counter[label]
            percentage = count / total_test * 100
            class_name = class_names.get(label, f'unknown_{label}')
            print(f"{class_name:<12} {label:<8} {count:<8} {percentage:<10.2f}%")
        
        # 总体统计
        all_labels = train_labels + test_labels
        all_counter = Counter(all_labels)
        total_all = len(all_labels)
        
        print(f"\n总体标签分布 (总计: {total_all} 个样本):")
        print(f"{'类别':<12} {'标签':<8} {'数量':<8} {'比例':<10}")
        print("-" * 45)
        
        for label in sorted(all_counter.keys()):
            count = all_counter[label]
            percentage = count / total_all * 100
            class_name = class_names.get(label, f'unknown_{label}')
            print(f"{class_name:<12} {label:<8} {count:<8} {percentage:<10.2f}%")
        
        return {
            'train': train_counter,
            'test': test_counter,
            'all': all_counter,
            'class_names': class_names
        }
        
    except FileNotFoundError:
        print("错误: 找不到数据文件 '../data/iemocap_multimodal_features.pkl'")
        return None
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def count_meld_labels():
    """统计MELD数据集的标签分布"""
    print("正在加载MELD数据集...")
    
    try:
        # 加载数据
        with open('../data/meld_multimodal_features.pkl', 'rb') as f:
            data = pickle.load(f)
        
        videoIDs, videoSpeakers, videoLabels, videoText, roberta2, roberta3, roberta4, \
        videoAudio, videoVisual, videoSentence, trainVid, testVid, _ = data
        
        # 定义类别名称
        class_names = {
            0: 'neutral',
            1: 'surprise',
            2: 'fear', 
            3: 'sadness',
            4: 'joy',
            5: 'disgust',
            6: 'anger'
        }
        
        print("\n" + "="*60)
        print("MELD 数据集标签统计")
        print("="*60)
        
        # 统计训练集标签
        train_labels = []
        for vid in trainVid:
            train_labels.extend(videoLabels[vid])
        
        train_counter = Counter(train_labels)
        total_train = len(train_labels)
        
        print(f"\n训练集标签分布 (总计: {total_train} 个样本):")
        print(f"{'类别':<12} {'标签':<8} {'数量':<8} {'比例':<10}")
        print("-" * 45)
        
        for label in sorted(train_counter.keys()):
            count = train_counter[label]
            percentage = count / total_train * 100
            class_name = class_names.get(label, f'unknown_{label}')
            print(f"{class_name:<12} {label:<8} {count:<8} {percentage:<10.2f}%")
        
        # 统计测试集标签
        test_labels = []
        for vid in testVid:
            test_labels.extend(videoLabels[vid])
        
        test_counter = Counter(test_labels)
        total_test = len(test_labels)
        
        print(f"\n测试集标签分布 (总计: {total_test} 个样本):")
        print(f"{'类别':<12} {'标签':<8} {'数量':<8} {'比例':<10}")
        print("-" * 45)
        
        for label in sorted(test_counter.keys()):
            count = test_counter[label]
            percentage = count / total_test * 100
            class_name = class_names.get(label, f'unknown_{label}')
            print(f"{class_name:<12} {label:<8} {count:<8} {percentage:<10.2f}%")
        
        return {
            'train': train_counter,
            'test': test_counter,
            'class_names': class_names
        }
        
    except FileNotFoundError:
        print("错误: 找不到数据文件 '../data/meld_multimodal_features.pkl'")
        return None
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='统计训练集标签分布')
    parser.add_argument('--dataset', choices=['IEMOCAP', 'MELD', 'both'], 
                       default='both', help='选择要统计的数据集')
    
    args = parser.parse_args()
    
    if args.dataset in ['IEMOCAP', 'both']:
        iemocap_stats = count_iemocap_labels()
    
    if args.dataset in ['MELD', 'both']:
        meld_stats = count_meld_labels()
    
    print("\n" + "="*60)
    print("统计完成!")
    print("="*60)

if __name__ == "__main__":
    main()
