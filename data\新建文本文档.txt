0 --hap
1 --sad
2 --neu
3 --ang
4 --exc
5 --fru

Emotion 1: 23
Emotion 0: 5
Emotion 2: 1
Emotion 4: 1


------------iemocap.pkl---------
ata = [
    # ================== 字典0：会话样本标识 (videoIDs) ==================
    {
        "Ses02F_script01_3": [
            "Ses02F_script01_3_M001",  # 样本1唯一标识符（格式：会话ID_参与者ID）
            "Ses02F_script01_3_F009",  # 样本2（F表示女性参与者）
            "Ses02F_script01_3_M010",  # 样本3（M表示男性参与者）
            # ...共30个样本标识，样本中其实含有60多句话，但是他们情感标签是xxx的直接不作为样本
        ],
        "Ses03F_script03_1": [
            "Ses03F_script03_1_F001",  # 其他会话的样本标识
            "Ses03F_script03_1_M002",
            # ...
        ],
        # 其他会话键...151个
    },
    
    # ================== 字典1：说话者性别 (videoSpeakers) ==================
    {
        "Ses02F_script01_3": [
            'M',   # 样本1说话者性别（对应videoIDs中的M001）
            'F',   # 样本2说话者性别（对应F009）
            'M',   # 样本3说话者性别（对应M010）
            # ...与videoIDs长度一致的性别序列
        ],
        "Ses03F_script03_1": [
            'F',   # 女性说话者
            'M',   # 男性说话者
            # ...
        ],
        # 其他会话键...
    },
    
    # ================== 字典2：情感标签 (videoLabels) ==================
    {
        "Ses02F_script01_3": [
            2,  # 样本1情感标签（2=sad）
            1,  # 样本2情感标签（1=happy）
            4,  # 样本3情感标签（4=frustrated）
            # ...标签与样本一一对应
        ],
        "Ses03F_script03_1": [
            0,  # neutral（中性）
            3,  # angry（愤怒）
            # ...
        ],
        # 其他会话键...
    },
    
    # ================== 字典3：文本特征 (videoText) ================== 
    {
        "Ses03F_impro06": [
            np.array([2.48, 0.34, -0.58, ...], dtype=np.float32),  # 样本1第一句话文本特征向量 1024维
            np.array([0.29, -0.12, -1.01, ...], dtype=np.float32), # 样本2文本特征向量
            # ...每个样本对应1024维向量
        ],
        "Ses03F_script03_1": [
            np.array([-0.11, 0.40, -0.75, ...], dtype=np.float32),
            # ...
        ],
        # 其他会话键...
    },

    # ================== 字典4：RoBERTa第二层特征 (roberta2) ==================
    {
        "Ses03F_impro06": [
            np.array([1.2429882, 0.3213829, ...], dtype=np.float32),  # 第1句第二层特征向量
            np.array([0.3246504, 0.17944804, ...], dtype=np.float32), # 第2句第二层特征
            # ...共53个样本,53句话，每个特征向量维度与模型配置相关
        ],
        "Ses01M_script01_2": [
            np.array([0.7449429, 0.49165523, ...], dtype=np.float32),
            # ...
        ],
        # 其他会话键...
    },

    # ================== 字典5：RoBERTa第三层特征 (roberta3) ==================
    {
        "Ses03F_impro06": [
            np.array([0.32900286, -0.00850558, ...], dtype=np.float32),  # 第三层特征
            np.array([0.17911889, 0.11555737, ...], dtype=np.float32),
            # ...特征维度与第二层一致（如1024维）
        ],
        # 其他会话键结构相同...
    },

    # ================== 字典6：RoBERTa第四层特征 (roberta4) ==================
    {
        "Ses03F_impro06": [
            np.array([0.10631318, 0.04574243, ...], dtype=np.float32),  # 第四层特征
            np.array([0.02210089, 0.0417283, ...], dtype=np.float32),
            # ...通常第四层包含最抽象的语言表示
        ],
        # 其他会话键结构相同...
    },
        
    # ================== 字典7：音频特征 (videoAudio) ==================
    {
        "Ses02F_script01_3": np.array(
            [
                [-0.5947186, -0.91704124, ..., -0.9670294],  # 第1句话音频特征
                [-0.38297018, 0.09654831, ..., -0.66208136], # 第2句话特征
                # ...共30个样本，每帧含1582维声学特征（如MFCC）
            ], 
            dtype=np.float32
        ),
        "Ses03F_script03_1": np.array(
            [
                [2.3649464, 1.292845, ..., 1.6168313],       # 第1句话特征
                # ...其他帧
            ],
            dtype=np.float32
        ),
        # 其他会话键结构相同...
    },
 
    # ================== 字典8：视觉特征 (videoFeatures) ==================
    {   
        "Ses02F_script01_3": [
            # ...相同结构...30个样本
        ],
        "Ses03F_impro06": [
            np.array([5.66686876e-03, 1.05400023e-03, ...], dtype=np.float32),  # 样本1的视觉特征
            np.array([...], dtype=np.float32),  # 样本2特征（shape=(342,)）  342维
            # ...每个会话包含与videoIDs相同数量的样本 53个
        ]
        # 其他会话键...
    },
    
    # ================== 字典9：原始文本数据 (videoText) ==================
    {
        "Ses02F_script01_3": [
            "You're not sorry you came?",                # 样本0的原始文本 Ses02F_script01_3_M001
            'from her point of view, why else would I come?',  # 样本1的文本
            "You feel it's wrong here, don't you?...",   # 样本2的文本
            # ...共30个样本（与同会话ID的videoIDs样本数一致）
        ],
        "Ses03F_script03_1": [
            'I almost got married two years ago.',       # 该会话的样本0
            'You started to write me and--',             # 样本1
            # ...其他样本文本
        ],
        # 其他151个会话键结构相同...
    },
    
    # 后续元素...
]

}
]