import pickle
import numpy as np


# 打开pkl文件
with open('iemocap_multimodal_features.pkl', 'rb') as file:
    data = pickle.load(file)
for i, item in enumerate(data):
    print(f"元素 {i} 类型：", type(item))

# 假设data是一个包含至少10个字典的列表
# data = [...] 这里省略了data的具体内容

# 循环访问data的前10个元素
for i in [1,2,9]:
    # 打印当前元素的类型（应该是dict类型）
    print(type(data[i]))
    
    # 打印当前元素的所有键
    #print(data[i].keys())

    # 访问并打印特定键的值列表
    if 'Ses02F_script02_2' in data[i]:
        # print(data[i]['Ses02F_script02_2'])
        # # 打印特定键的值列表的长度
        # print(len(data[i]['Ses02F_script02_2']))
        # # # 打印特定键的值列表中第二个元素（索引为1）
        #label4,2,5,3,1,0
        for j in [0,1,3,7,27,64]:
            print(data[i]['Ses02F_script02_2'][j])
        
    
    else:
        print(f"元素{i} 中没有找到 'Ses02F_script01_3' 这个键")
    print("------------------------------------------")
# Well, so what do you think? M000  exc
# About what?  F000  neu
# It's ridiculous.  F001  fru
# God damn it, Augie, don't ask me that.  I hate it when you ask me that.  You always ask me that.  It's insulting.  F005 ang
# Do you remember the first time we came to see it?  It was about four years ago, right after we got married.       F015 sad
# I don't know.  It seemed like a pretty good spot to me.  I mean, look at the view you have of the moon from here. M041 hap

