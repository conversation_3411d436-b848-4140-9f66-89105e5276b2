import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class IntraBatchContrastiveLoss(nn.Module):
    """
    批内对比损失，在每个batch内维持滑动窗口队列
    """
    def __init__(self, hidden_dim, queue_size=10, temperature=0.07):
        super(IntraBatchContrastiveLoss, self).__init__()
        self.queue_size = queue_size
        self.temperature = temperature
        self.hidden_dim = hidden_dim

    def forward(self, text_features, audio_features, mask=None):
        """
        批内对比损失计算

        Args:
            text_features: (batch_size, seq_len, hidden_dim)
            audio_features: (batch_size, seq_len, hidden_dim)
            mask: (batch_size, seq_len) 掩码，1表示有效位置

        Returns:
            intra_modal_loss: 模态内对比损失
            inter_modal_loss: 模态间对比损失
        """
        batch_size, seq_len, hidden_dim = text_features.shape

        # 归一化特征
        text_features = F.normalize(text_features, dim=-1)
        audio_features = F.normalize(audio_features, dim=-1)

        total_intra_loss = 0.0
        total_inter_loss = 0.0
        total_count = 0

        # 对每个样本分别处理
        for b in range(batch_size):
            # 获取当前样本的有效长度
            if mask is not None:
                valid_length = int(mask[b].sum().item())
            else:
                valid_length = int(seq_len)

            if valid_length <= 1:
                continue  # 跳过长度太短的序列

            # 获取当前样本的有效特征
            text_seq = text_features[b, :valid_length, :]  # (valid_length, hidden_dim)
            audio_seq = audio_features[b, :valid_length, :]  # (valid_length, hidden_dim)

            # 对每个位置计算对比损失
            for i in range(valid_length):
                # 当前位置的特征
                text_current = text_seq[i:i+1, :]  # (1, hidden_dim)
                audio_current = audio_seq[i:i+1, :]  # (1, hidden_dim)

                # 构建队列：取前面的特征作为队列（最多queue_size个）
                queue_start = max(0, i - self.queue_size + 1)
                queue_end = i

                if queue_end > queue_start:  # 确保队列不为空
                    # 文本队列和音频队列
                    text_queue = text_seq[queue_start:queue_end, :]  # (queue_len, hidden_dim)
                    audio_queue = audio_seq[queue_start:queue_end, :]  # (queue_len, hidden_dim)
                    queue_len = queue_end - queue_start

                    # 1. 模态内对比损失
                    # 文本当前位置 vs 文本队列
                    text_sim = torch.mm(text_current, text_queue.T) / self.temperature  # (1, queue_len)
                    # 音频当前位置 vs 音频队列
                    audio_sim = torch.mm(audio_current, audio_queue.T) / self.temperature  # (1, queue_len)

                    # 标签全为0（所有队列位置都是负样本）
                    intra_labels = torch.zeros(1, device=text_features.device, dtype=torch.long)

                    # 计算交叉熵损失（希望与队列中所有位置的相似度都低）
                    text_intra_loss = F.cross_entropy(text_sim, intra_labels)
                    audio_intra_loss = F.cross_entropy(audio_sim, intra_labels)

                    total_intra_loss += (text_intra_loss + audio_intra_loss)

                    # 2. 模态间对比损失
                    # 文本当前位置 vs 音频队列
                    text_cross_sim = torch.mm(text_current, audio_queue.T) / self.temperature  # (1, queue_len)
                    # 音频当前位置 vs 文本队列
                    audio_cross_sim = torch.mm(audio_current, text_queue.T) / self.temperature  # (1, queue_len)

                    # 标签：最后一个位置为1（最近的位置是正样本），其余为0
                    inter_labels = torch.tensor([queue_len - 1], device=text_features.device, dtype=torch.long)

                    # 计算交叉熵损失
                    text_inter_loss = F.cross_entropy(text_cross_sim, inter_labels)
                    audio_inter_loss = F.cross_entropy(audio_cross_sim, inter_labels)

                    total_inter_loss += (text_inter_loss + audio_inter_loss)

                    total_count += 1

        # 计算平均损失
        if total_count > 0:
            intra_modal_loss = total_intra_loss / total_count
            inter_modal_loss = total_inter_loss / total_count
        else:
            intra_modal_loss = torch.tensor(0.0, device=text_features.device, requires_grad=True)
            inter_modal_loss = torch.tensor(0.0, device=text_features.device, requires_grad=True)

        return intra_modal_loss, inter_modal_loss


class ContrastiveLossTest:
    """测试对比损失函数的类"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
    def create_test_data(self, batch_size=3, max_seq_len=12, hidden_dim=64):
        """
        创建变长的测试数据，模拟真实的对话场景

        Args:
            batch_size: 批大小
            max_seq_len: 最大序列长度
            hidden_dim: 隐藏维度

        Returns:
            text_features, audio_features, umask, qmask, lengths
        """
        # 创建随机的序列长度（不要太长，便于测试）
        seq_lengths = torch.randint(3, max_seq_len + 1, (batch_size,))
        print(f"Sequence lengths: {seq_lengths.tolist()}")

        # 创建特征矩阵 (batch_size, max_seq_len, hidden_dim)
        text_features = torch.randn(batch_size, max_seq_len, hidden_dim, device=self.device)
        audio_features = torch.randn(batch_size, max_seq_len, hidden_dim, device=self.device)

        # 创建umask (utterance mask) - 标记有效的话语位置
        umask = torch.zeros(batch_size, max_seq_len, device=self.device)
        for i, length in enumerate(seq_lengths):
            umask[i, :length] = 1

        # 创建qmask (speaker mask) - 标记说话人 (batch_size, max_seq_len, 2)
        # 假设有2个说话人，随机分配
        qmask = torch.zeros(batch_size, max_seq_len, 2, device=self.device)
        for i, length in enumerate(seq_lengths):
            for j in range(length):
                speaker = torch.randint(0, 2, (1,)).item()  # 随机选择说话人0或1
                qmask[i, j, speaker] = 1

        # lengths列表 - 每个样本的实际长度
        lengths = seq_lengths.tolist()

        return text_features, audio_features, umask, qmask, lengths
    
    def test_basic_functionality(self):
        """测试基本功能"""
        print("\n=== 测试基本功能 ===")

        # 创建对比损失函数
        contrastive_loss = IntraBatchContrastiveLoss(
            hidden_dim=64,
            queue_size=5,
            temperature=0.1
        ).to(self.device)

        # 创建测试数据
        text_features, audio_features, umask, qmask, lengths = self.create_test_data(
            batch_size=3, max_seq_len=8, hidden_dim=64
        )

        print(f"Text features shape: {text_features.shape}")
        print(f"Audio features shape: {audio_features.shape}")
        print(f"UMask shape: {umask.shape}")
        print(f"QMask shape: {qmask.shape}")
        print(f"Lengths: {lengths}")
        print(f"UMask sum per sample: {umask.sum(dim=1).tolist()}")

        # 计算损失
        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)

            print("\n✅ 成功计算损失:")
            print(f"  模态内损失: {intra_loss.item():.6f}")
            print(f"  模态间损失: {inter_loss.item():.6f}")
            print(f"  损失是否需要梯度: intra={intra_loss.requires_grad}, inter={inter_loss.requires_grad}")

            # 检查损失值是否合理
            if intra_loss.item() < 0 or inter_loss.item() < 0:
                print(f"⚠️  警告: 发现负损失值!")

            return True

        except Exception as e:
            print(f"❌ 计算损失时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_different_queue_sizes(self):
        """测试不同队列大小"""
        print("\n=== 测试不同队列大小 ===")

        text_features, audio_features, umask, qmask, lengths = self.create_test_data(
            batch_size=2, max_seq_len=10, hidden_dim=32
        )

        queue_sizes = [2, 3, 5, 8]

        for queue_size in queue_sizes:
            contrastive_loss = IntraBatchContrastiveLoss(
                hidden_dim=32,
                queue_size=queue_size,
                temperature=0.1
            ).to(self.device)

            try:
                intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
                status = "⚠️" if (intra_loss.item() < 0 or inter_loss.item() < 0) else "✅"
                print(f"  队列大小 {queue_size:2d}: {status} intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}")
            except Exception as e:
                print(f"  队列大小 {queue_size:2d}: ❌ 错误 - {e}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("\n=== 测试边界情况 ===")

        contrastive_loss = IntraBatchContrastiveLoss(
            hidden_dim=32,
            queue_size=3,
            temperature=0.1
        ).to(self.device)

        # 测试1: 很短的序列
        print("测试1: 很短的序列")
        text_features = torch.randn(2, 4, 32, device=self.device)
        audio_features = torch.randn(2, 4, 32, device=self.device)
        umask = torch.tensor([[1, 1, 0, 0], [1, 1, 1, 0]], device=self.device, dtype=torch.float)

        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
            status = "⚠️" if (intra_loss.item() < 0 or inter_loss.item() < 0) else "✅"
            print(f"  {status} 短序列: intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}")
        except Exception as e:
            print(f"  ❌ 短序列错误: {e}")

        # 测试2: 没有掩码
        print("测试2: 没有掩码")
        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, None)
            status = "⚠️" if (intra_loss.item() < 0 or inter_loss.item() < 0) else "✅"
            print(f"  {status} 无掩码: intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}")
        except Exception as e:
            print(f"  ❌ 无掩码错误: {e}")

        # 测试3: 单个样本，较长序列
        print("测试3: 单个样本")
        text_features = torch.randn(1, 6, 32, device=self.device)
        audio_features = torch.randn(1, 6, 32, device=self.device)
        umask = torch.tensor([[1, 1, 1, 1, 1, 0]], device=self.device, dtype=torch.float)

        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
            status = "⚠️" if (intra_loss.item() < 0 or inter_loss.item() < 0) else "✅"
            print(f"  {status} 单样本: intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}")
        except Exception as e:
            print(f"  ❌ 单样本错误: {e}")

        # 测试4: 极短序列（长度为1）
        print("测试4: 极短序列（长度为1）")
        text_features = torch.randn(2, 2, 32, device=self.device)
        audio_features = torch.randn(2, 2, 32, device=self.device)
        umask = torch.tensor([[1, 0], [1, 0]], device=self.device, dtype=torch.float)

        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
            print(f"  ✅ 极短序列: intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}")
        except Exception as e:
            print(f"  ❌ 极短序列错误: {e}")
    
    def test_gradient_flow(self):
        """测试梯度流"""
        print("\n=== 测试梯度流 ===")

        contrastive_loss = IntraBatchContrastiveLoss(
            hidden_dim=32,
            queue_size=4,
            temperature=0.1
        ).to(self.device)

        # 创建需要梯度的输入
        text_features = torch.randn(2, 6, 32, device=self.device, requires_grad=True)
        audio_features = torch.randn(2, 6, 32, device=self.device, requires_grad=True)
        umask = torch.tensor([[1, 1, 1, 1, 0, 0], [1, 1, 1, 1, 1, 0]], device=self.device, dtype=torch.float)

        try:
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
            total_loss = intra_loss + inter_loss

            print(f"  损失值: intra={intra_loss.item():.4f}, inter={inter_loss.item():.4f}, total={total_loss.item():.4f}")

            # 反向传播
            total_loss.backward()

            print("  ✅ 梯度计算成功")
            print(f"  Text features 梯度范数: {text_features.grad.norm().item():.6f}")
            print(f"  Audio features 梯度范数: {audio_features.grad.norm().item():.6f}")

            # 检查是否有负损失
            if intra_loss.item() < 0 or inter_loss.item() < 0:
                print("  ⚠️  警告: 检测到负损失值!")

        except Exception as e:
            print(f"  ❌ 梯度计算错误: {e}")
            import traceback
            traceback.print_exc()

    def test_negative_loss_investigation(self):
        """专门测试负损失问题"""
        print("\n=== 负损失调查测试 ===")

        # 测试不同的温度参数
        temperatures = [0.01, 0.07, 0.1, 0.5, 1.0]

        for temp in temperatures:
            print(f"\n温度参数: {temp}")
            contrastive_loss = IntraBatchContrastiveLoss(
                hidden_dim=32,
                queue_size=3,
                temperature=temp
            ).to(self.device)

            # 创建简单的测试数据
            text_features, audio_features, umask, _, _ = self.create_test_data(
                batch_size=2, max_seq_len=5, hidden_dim=32
            )

            try:
                intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)

                status_intra = "❌" if intra_loss.item() < 0 else "✅"
                status_inter = "❌" if inter_loss.item() < 0 else "✅"

                print(f"  {status_intra} 模态内损失: {intra_loss.item():.6f}")
                print(f"  {status_inter} 模态间损失: {inter_loss.item():.6f}")

                # 如果有负损失，打印更多调试信息
                if intra_loss.item() < 0 or inter_loss.item() < 0:
                    print(f"    ⚠️  发现负损失! 温度={temp}")

            except Exception as e:
                print(f"  ❌ 错误: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("开始对比损失函数测试...")
        print("=" * 50)

        success_count = 0
        total_tests = 5

        # 基本功能测试
        if self.test_basic_functionality():
            success_count += 1

        # 不同队列大小测试
        try:
            self.test_different_queue_sizes()
            success_count += 1
        except Exception as e:
            print(f"❌ 队列大小测试失败: {e}")

        # 边界情况测试
        try:
            self.test_edge_cases()
            success_count += 1
        except Exception as e:
            print(f"❌ 边界情况测试失败: {e}")

        # 梯度流测试
        try:
            self.test_gradient_flow()
            success_count += 1
        except Exception as e:
            print(f"❌ 梯度流测试失败: {e}")

        # 负损失调查测试
        try:
            self.test_negative_loss_investigation()
            success_count += 1
        except Exception as e:
            print(f"❌ 负损失调查测试失败: {e}")

        print("\n" + "=" * 50)
        print("=== 测试总结 ===")
        print(f"通过测试: {success_count}/{total_tests}")
        print(f"测试{'成功' if success_count == total_tests else '部分失败'}")

        if success_count < total_tests:
            print("\n建议检查:")
            print("1. CrossEntropy损失是否输入了正确的log probabilities")
            print("2. 温度参数是否过小导致数值不稳定")
            print("3. 标签索引是否超出范围")


if __name__ == "__main__":
    # 运行测试
    tester = ContrastiveLossTest()
    tester.run_all_tests()
