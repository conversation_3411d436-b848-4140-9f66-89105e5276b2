D:\Anaconda\envs\ljp\python.exe "E:\Pycharm WorkingSpace\MMERC\SDT-main\train.py"
Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=2, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 2
total parameters: 79687704
training parameters: 79687704

*** 新的最优F1分数: 15.3983% (Epoch 1) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 10.4167% (144 样本)
sad: 0.0000% (245 样本)
neutral: 42.4479% (384 样本)
angry: 32.9412% (170 样本)
excited: 23.7458% (299 样本)
frustrated: 2.8871% (381 样本)

每个类别的F1分数:
happy: 13.3333%
sad: 0.0000%
neutral: 25.9554%
angry: 30.8540%
excited: 20.0282%
frustrated: 4.9107%

总体指标:
总体准确率: 19.4701%
加权准确率 (W_ACC, 按测试集分布): 19.4701%
总体F1分数 (不加权宏平均): 15.8469%
手动计算宏平均F1: 15.8469%
加权F1 (W_F1, 按测试集分布): 15.3983%
============================================================
epoch: 1, train_loss: 13.7847, train_acc: 19.6041, train_fscore: 16.6898, test_loss: 12.6817, test_acc: 19.4701, test_fscore: 15.3983, time: 16.83 sec

*** 新的最优F1分数: 34.7204% (Epoch 2) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 22.2222% (144 样本)
sad: 91.4286% (245 样本)
neutral: 4.6875% (384 样本)
angry: 49.4118% (170 样本)
excited: 29.0970% (299 样本)
frustrated: 49.8688% (381 样本)

每个类别的F1分数:
happy: 23.7918%
sad: 48.7486%
neutral: 8.0357%
angry: 49.5575%
excited: 42.1308%
frustrated: 44.2890%

总体指标:
总体准确率: 39.1251%
加权准确率 (W_ACC, 按测试集分布): 39.1251%
总体F1分数 (不加权宏平均): 36.0922%
手动计算宏平均F1: 36.0922%
加权F1 (W_F1, 按测试集分布): 34.7204%
============================================================
epoch: 2, train_loss: 11.8288, train_acc: 27.0052, train_fscore: 23.7736, test_loss: 11.7283, test_acc: 39.1251, test_fscore: 34.7204, time: 16.6 sec

*** 新的最优F1分数: 47.6103% (Epoch 3) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 32.6389% (144 样本)
sad: 75.1020% (245 样本)
neutral: 19.2708% (384 样本)
angry: 71.7647% (170 样本)
excited: 65.5518% (299 样本)
frustrated: 41.9948% (381 样本)

每个类别的F1分数:
happy: 25.5435%
sad: 73.8956%
neutral: 27.1560%
angry: 43.9640%
excited: 67.1233%
frustrated: 45.9770%

总体指标:
总体准确率: 48.2440%
加权准确率 (W_ACC, 按测试集分布): 48.2440%
总体F1分数 (不加权宏平均): 47.2765%
手动计算宏平均F1: 47.2765%
加权F1 (W_F1, 按测试集分布): 47.6103%
============================================================
epoch: 3, train_loss: 11.1331, train_acc: 47.2117, train_fscore: 44.166, test_loss: 11.0066, test_acc: 48.244, test_fscore: 47.6103, time: 16.17 sec

*** 新的最优F1分数: 58.6291% (Epoch 4) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 29.1667% (144 样本)
sad: 77.5510% (245 样本)
neutral: 42.9688% (384 样本)
angry: 79.4118% (170 样本)
excited: 75.9197% (299 样本)
frustrated: 53.5433% (381 样本)

每个类别的F1分数:
happy: 30.3249%
sad: 71.9697%
neutral: 47.8955%
angry: 64.2857%
excited: 75.2902%
frustrated: 55.9671%

总体指标:
总体准确率: 59.3346%
加权准确率 (W_ACC, 按测试集分布): 59.3346%
总体F1分数 (不加权宏平均): 57.6222%
手动计算宏平均F1: 57.6222%
加权F1 (W_F1, 按测试集分布): 58.6291%
============================================================
epoch: 4, train_loss: 10.2599, train_acc: 54.3201, train_fscore: 50.5072, test_loss: 10.6597, test_acc: 59.3346, test_fscore: 58.6291, time: 16.15 sec
epoch: 5, train_loss: 9.8857, train_acc: 61.1015, train_fscore: 61.4022, test_loss: 10.5878, test_acc: 57.7942, test_fscore: 57.1422, time: 16.22 sec

*** 新的最优F1分数: 63.0737% (Epoch 6) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 22.9167% (144 样本)
sad: 84.0816% (245 样本)
neutral: 53.6458% (384 样本)
angry: 68.2353% (170 样本)
excited: 79.2642% (299 样本)
frustrated: 64.0420% (381 样本)

每个类别的F1分数:
happy: 30.0000%
sad: 75.0455%
neutral: 57.5419%
angry: 65.5367%
excited: 75.2381%
frustrated: 62.8057%

总体指标:
总体准确率: 64.2021%
加权准确率 (W_ACC, 按测试集分布): 64.2021%
总体F1分数 (不加权宏平均): 61.0280%
手动计算宏平均F1: 61.0280%
加权F1 (W_F1, 按测试集分布): 63.0737%
============================================================
epoch: 6, train_loss: 9.8971, train_acc: 61.6867, train_fscore: 59.0229, test_loss: 10.3769, test_acc: 64.2021, test_fscore: 63.0737, time: 16.02 sec
epoch: 7, train_loss: 9.5936, train_acc: 67.7969, train_fscore: 67.2209, test_loss: 9.9873, test_acc: 62.1688, test_fscore: 62.9514, time: 15.99 sec

*** 新的最优F1分数: 65.3595% (Epoch 8) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 50.6944% (144 样本)
sad: 71.4286% (245 样本)
neutral: 60.1562% (384 样本)
angry: 72.9412% (170 样本)
excited: 77.9264% (299 样本)
frustrated: 57.4803% (381 样本)

每个类别的F1分数:
happy: 42.1965%
sad: 73.2218%
neutral: 63.0286%
angry: 65.2632%
excited: 77.7963%
frustrated: 61.6901%

总体指标:
总体准确率: 65.0031%
加权准确率 (W_ACC, 按测试集分布): 65.0031%
总体F1分数 (不加权宏平均): 63.8661%
手动计算宏平均F1: 63.8661%
加权F1 (W_F1, 按测试集分布): 65.3595%
============================================================
epoch: 8, train_loss: 9.3882, train_acc: 68.7435, train_fscore: 68.5397, test_loss: 10.2097, test_acc: 65.0031, test_fscore: 65.3595, time: 16.17 sec
epoch: 9, train_loss: 9.4397, train_acc: 69.1566, train_fscore: 68.5799, test_loss: 10.1731, test_acc: 63.9556, test_fscore: 64.5756, time: 16.22 sec

*** 新的最优F1分数: 66.6358% (Epoch 10) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.8889% (144 样本)
sad: 78.3673% (245 样本)
neutral: 61.4583% (384 样本)
angry: 78.8235% (170 样本)
excited: 69.2308% (299 样本)
frustrated: 55.6430% (381 样本)

每个类别的F1分数:
happy: 48.1675%
sad: 76.9539%
neutral: 66.5726%
angry: 65.8477%
excited: 75.0000%
frustrated: 60.8321%

总体指标:
总体准确率: 66.1121%
加权准确率 (W_ACC, 按测试集分布): 66.1121%
总体F1分数 (不加权宏平均): 65.5623%
手动计算宏平均F1: 65.5623%
加权F1 (W_F1, 按测试集分布): 66.6358%
============================================================
epoch: 10, train_loss: 9.1138, train_acc: 70.6713, train_fscore: 70.5077, test_loss: 9.7851, test_acc: 66.1121, test_fscore: 66.6358, time: 16.18 sec
----------best F-Score: 66.6358
              precision    recall  f1-score   support

           0     0.3866    0.6389    0.4817       144
           1     0.7559    0.7837    0.7695       245
           2     0.7262    0.6146    0.6657       384
           3     0.5654    0.7882    0.6585       170
           4     0.8182    0.6923    0.7500       299
           5     0.6709    0.5564    0.6083       381

    accuracy                         0.6611      1623
   macro avg     0.6538    0.6790    0.6556      1623
weighted avg     0.6877    0.6611    0.6664      1623

[[ 92   6   6   4  34   2]
 [  7 192  18   2   0  26]
 [ 53  29 236  18   5  43]
 [  0   1   3 134   0  32]
 [ 81   0  10   0 207   1]
 [  5  26  52  79   7 212]]


*** 新的最优F1分数: 67.7834% (Epoch 11) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.8611% (144 样本)
sad: 61.6327% (245 样本)
neutral: 69.5312% (384 样本)
angry: 71.7647% (170 样本)
excited: 74.9164% (299 样本)
frustrated: 65.3543% (381 样本)

每个类别的F1分数:
happy: 46.3343%
sad: 72.0764%
neutral: 67.1698%
angry: 67.7778%
excited: 77.3748%
frustrated: 66.2234%

总体指标:
总体准确率: 67.2828%
加权准确率 (W_ACC, 按测试集分布): 67.2828%
总体F1分数 (不加权宏平均): 66.1594%
手动计算宏平均F1: 66.1594%
加权F1 (W_F1, 按测试集分布): 67.7834%
============================================================
epoch: 11, train_loss: 9.1896, train_acc: 71.0499, train_fscore: 70.7165, test_loss: 10.0399, test_acc: 67.2828, test_fscore: 67.7834, time: 16.27 sec
epoch: 12, train_loss: 9.152, train_acc: 71.0671, train_fscore: 70.7948, test_loss: 9.7457, test_acc: 66.5434, test_fscore: 66.9953, time: 16.11 sec
epoch: 13, train_loss: 8.9572, train_acc: 71.9105, train_fscore: 71.5583, test_loss: 9.9712, test_acc: 64.3253, test_fscore: 64.9698, time: 16.01 sec

*** 新的最优F1分数: 68.9128% (Epoch 14) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 64.5833% (144 样本)
sad: 75.1020% (245 样本)
neutral: 66.4062% (384 样本)
angry: 74.1176% (170 样本)
excited: 70.5686% (299 样本)
frustrated: 63.2546% (381 样本)

每个类别的F1分数:
happy: 51.3812%
sad: 78.2979%
neutral: 67.2823%
angry: 68.6649%
excited: 76.0360%
frustrated: 65.6676%

总体指标:
总体准确率: 68.3919%
加权准确率 (W_ACC, 按测试集分布): 68.3919%
总体F1分数 (不加权宏平均): 67.8883%
手动计算宏平均F1: 67.8883%
加权F1 (W_F1, 按测试集分布): 68.9128%
============================================================
epoch: 14, train_loss: 9.0356, train_acc: 72.2719, train_fscore: 72.0529, test_loss: 9.6713, test_acc: 68.3919, test_fscore: 68.9128, time: 15.99 sec
epoch: 15, train_loss: 8.9624, train_acc: 73.9931, train_fscore: 73.8475, test_loss: 9.6804, test_acc: 66.5434, test_fscore: 67.152, time: 15.99 sec
epoch: 16, train_loss: 8.8406, train_acc: 72.6334, train_fscore: 72.4379, test_loss: 9.9077, test_acc: 67.3444, test_fscore: 67.3097, time: 16.0 sec
epoch: 17, train_loss: 8.9776, train_acc: 75.3012, train_fscore: 75.0654, test_loss: 9.8526, test_acc: 66.6667, test_fscore: 67.5506, time: 16.01 sec
epoch: 18, train_loss: 8.8778, train_acc: 73.9759, train_fscore: 73.7889, test_loss: 9.4692, test_acc: 68.0222, test_fscore: 68.6645, time: 16.35 sec
epoch: 19, train_loss: 8.6762, train_acc: 75.8176, train_fscore: 75.5785, test_loss: 9.7274, test_acc: 66.6051, test_fscore: 67.3235, time: 16.21 sec

*** 新的最优F1分数: 69.9387% (Epoch 20) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.1944% (144 样本)
sad: 76.7347% (245 样本)
neutral: 69.5312% (384 样本)
angry: 70.5882% (170 样本)
excited: 71.2375% (299 样本)
frustrated: 65.6168% (381 样本)

每个类别的F1分数:
happy: 53.3724%
sad: 78.9916%
neutral: 68.9922%
angry: 69.7674%
excited: 76.2075%
frustrated: 66.4894%

总体指标:
总体准确率: 69.5625%
加权准确率 (W_ACC, 按测试集分布): 69.5625%
总体F1分数 (不加权宏平均): 68.9701%
手动计算宏平均F1: 68.9701%
加权F1 (W_F1, 按测试集分布): 69.9387%
============================================================
epoch: 20, train_loss: 8.6998, train_acc: 76.3339, train_fscore: 76.2218, test_loss: 9.5727, test_acc: 69.5625, test_fscore: 69.9387, time: 16.01 sec
----------best F-Score: 69.9387
              precision    recall  f1-score   support

           0     0.4619    0.6319    0.5337       144
           1     0.8139    0.7673    0.7899       245
           2     0.6846    0.6953    0.6899       384
           3     0.6897    0.7059    0.6977       170
           4     0.8192    0.7124    0.7621       299
           5     0.6739    0.6562    0.6649       381

    accuracy                         0.6956      1623
   macro avg     0.6905    0.6948    0.6897      1623
weighted avg     0.7072    0.6956    0.6994      1623

[[ 91   4  11   0  36   2]
 [  2 188  28   2   0  25]
 [ 34  22 267   8   6  47]
 [  0   0   4 120   0  46]
 [ 68   0  17   0 213   1]
 [  2  17  63  44   5 250]]
epoch: 21, train_loss: 8.5994, train_acc: 75.8864, train_fscore: 75.6692, test_loss: 9.6115, test_acc: 66.9747, test_fscore: 67.5979, time: 16.3 sec
epoch: 22, train_loss: 8.7159, train_acc: 77.6076, train_fscore: 77.5239, test_loss: 9.7571, test_acc: 68.4535, test_fscore: 68.8806, time: 16.07 sec
epoch: 23, train_loss: 8.7652, train_acc: 76.8675, train_fscore: 76.7009, test_loss: 9.6762, test_acc: 68.5151, test_fscore: 68.9365, time: 16.0 sec
epoch: 24, train_loss: 8.6594, train_acc: 78.1928, train_fscore: 78.0205, test_loss: 9.5729, test_acc: 68.8848, test_fscore: 69.5324, time: 16.0 sec
epoch: 25, train_loss: 8.5231, train_acc: 78.5026, train_fscore: 78.4441, test_loss: 9.5343, test_acc: 68.8232, test_fscore: 69.2649, time: 16.01 sec
epoch: 26, train_loss: 8.5388, train_acc: 78.537, train_fscore: 78.4361, test_loss: 9.5654, test_acc: 68.6383, test_fscore: 69.0039, time: 16.18 sec

*** 新的最优F1分数: 70.6583% (Epoch 27) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 65.9722% (144 样本)
sad: 75.9184% (245 样本)
neutral: 69.0104% (384 样本)
angry: 78.8235% (170 样本)
excited: 76.5886% (299 样本)
frustrated: 61.6798% (381 样本)

每个类别的F1分数:
happy: 58.4615%
sad: 78.6469%
neutral: 69.8287%
angry: 69.0722%
excited: 78.6942%
frustrated: 65.3686%

总体指标:
总体准确率: 70.4868%
加权准确率 (W_ACC, 按测试集分布): 70.4868%
总体F1分数 (不加权宏平均): 70.0120%
手动计算宏平均F1: 70.0120%
加权F1 (W_F1, 按测试集分布): 70.6583%
============================================================
epoch: 27, train_loss: 8.4915, train_acc: 78.1928, train_fscore: 78.0176, test_loss: 9.4222, test_acc: 70.4868, test_fscore: 70.6583, time: 16.22 sec
epoch: 28, train_loss: 8.4666, train_acc: 79.346, train_fscore: 79.2235, test_loss: 9.6034, test_acc: 67.2212, test_fscore: 67.8375, time: 16.17 sec

*** 新的最优F1分数: 71.2711% (Epoch 29) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 67.3611% (144 样本)
sad: 75.1020% (245 样本)
neutral: 72.1354% (384 样本)
angry: 75.2941% (170 样本)
excited: 74.2475% (299 样本)
frustrated: 64.0420% (381 样本)

每个类别的F1分数:
happy: 58.0838%
sad: 80.1743%
neutral: 70.7535%
angry: 69.3767%
excited: 77.3519%
frustrated: 67.1252%

总体指标:
总体准确率: 70.9797%
加权准确率 (W_ACC, 按测试集分布): 70.9797%
总体F1分数 (不加权宏平均): 70.4776%
手动计算宏平均F1: 70.4776%
加权F1 (W_F1, 按测试集分布): 71.2711%
============================================================
epoch: 29, train_loss: 8.4432, train_acc: 78.8296, train_fscore: 78.7316, test_loss: 9.3497, test_acc: 70.9797, test_fscore: 71.2711, time: 16.16 sec
epoch: 30, train_loss: 8.3663, train_acc: 79.1222, train_fscore: 79.0602, test_loss: 9.4876, test_acc: 68.5767, test_fscore: 69.0785, time: 16.04 sec
----------best F-Score: 71.2711
              precision    recall  f1-score   support

           0     0.5105    0.6736    0.5808       144
           1     0.8598    0.7510    0.8017       245
           2     0.6942    0.7214    0.7075       384
           3     0.6432    0.7529    0.6938       170
           4     0.8073    0.7425    0.7735       299
           5     0.7052    0.6404    0.6713       381

    accuracy                         0.7098      1623
   macro avg     0.7034    0.7136    0.7048      1623
weighted avg     0.7210    0.7098    0.7127      1623

[[ 97   4   6   0  36   1]
 [  4 184  28   2   0  27]
 [ 31  16 277  12  12  36]
 [  0   0   5 128   0  37]
 [ 54   0  22   0 222   1]
 [  4  10  61  57   5 244]]
epoch: 31, train_loss: 8.3411, train_acc: 80.3614, train_fscore: 80.1953, test_loss: 9.4053, test_acc: 68.7616, test_fscore: 69.3623, time: 16.14 sec
epoch: 32, train_loss: 8.3445, train_acc: 79.9828, train_fscore: 79.9103, test_loss: 9.4023, test_acc: 70.7948, test_fscore: 70.9758, time: 16.03 sec
epoch: 33, train_loss: 8.3241, train_acc: 80.5336, train_fscore: 80.4667, test_loss: 9.523, test_acc: 69.9322, test_fscore: 70.3508, time: 16.01 sec
epoch: 34, train_loss: 8.3048, train_acc: 80.5164, train_fscore: 80.3799, test_loss: 9.6663, test_acc: 68.6999, test_fscore: 69.0568, time: 15.99 sec
epoch: 35, train_loss: 8.3258, train_acc: 81.0499, train_fscore: 81.0036, test_loss: 9.3974, test_acc: 70.1171, test_fscore: 70.308, time: 16.2 sec
epoch: 36, train_loss: 8.3778, train_acc: 81.0155, train_fscore: 80.8826, test_loss: 9.6367, test_acc: 69.1929, test_fscore: 69.7273, time: 16.35 sec
epoch: 37, train_loss: 8.4203, train_acc: 81.5491, train_fscore: 81.4382, test_loss: 9.5486, test_acc: 68.2686, test_fscore: 68.7336, time: 16.04 sec
epoch: 38, train_loss: 8.331, train_acc: 82.0138, train_fscore: 81.8892, test_loss: 9.7705, test_acc: 70.4251, test_fscore: 70.8197, time: 16.37 sec
epoch: 39, train_loss: 8.3234, train_acc: 81.9449, train_fscore: 81.8208, test_loss: 9.6618, test_acc: 69.9322, test_fscore: 70.3785, time: 16.01 sec

*** 新的最优F1分数: 71.4896% (Epoch 40) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 69.4444% (144 样本)
sad: 78.3673% (245 样本)
neutral: 71.0938% (384 样本)
angry: 75.8824% (170 样本)
excited: 76.5886% (299 样本)
frustrated: 61.4173% (381 样本)

每个类别的F1分数:
happy: 59.1716%
sad: 80.5031%
neutral: 71.7477%
angry: 68.6170%
excited: 78.5592%
frustrated: 65.8228%

总体指标:
总体准确率: 71.2877%
加权准确率 (W_ACC, 按测试集分布): 71.2877%
总体F1分数 (不加权宏平均): 70.7369%
手动计算宏平均F1: 70.7369%
加权F1 (W_F1, 按测试集分布): 71.4896%
============================================================
epoch: 40, train_loss: 8.2137, train_acc: 82.6162, train_fscore: 82.5404, test_loss: 9.409, test_acc: 71.2877, test_fscore: 71.4896, time: 16.18 sec
----------best F-Score: 71.4896
              precision    recall  f1-score   support

           0     0.5155    0.6944    0.5917       144
           1     0.8276    0.7837    0.8050       245
           2     0.7241    0.7109    0.7175       384
           3     0.6262    0.7588    0.6862       170
           4     0.8063    0.7659    0.7856       299
           5     0.7091    0.6142    0.6582       381

    accuracy                         0.7129      1623
   macro avg     0.7015    0.7213    0.7074      1623
weighted avg     0.7226    0.7129    0.7149      1623

[[100   3   5   0  35   1]
 [  4 192  21   3   0  25]
 [ 33  22 273  10  12  34]
 [  0   1   6 129   0  34]
 [ 52   1  15   0 229   2]
 [  5  13  57  64   8 234]]
epoch: 41, train_loss: 8.2074, train_acc: 82.5473, train_fscore: 82.4704, test_loss: 9.5974, test_acc: 68.7616, test_fscore: 68.9783, time: 16.01 sec
epoch: 42, train_loss: 8.1827, train_acc: 82.9088, train_fscore: 82.7856, test_loss: 9.5523, test_acc: 68.6999, test_fscore: 69.2928, time: 16.2 sec
epoch: 43, train_loss: 8.1328, train_acc: 83.7177, train_fscore: 83.6575, test_loss: 9.3198, test_acc: 69.5625, test_fscore: 69.934, time: 16.01 sec
epoch: 44, train_loss: 8.1254, train_acc: 82.5301, train_fscore: 82.3958, test_loss: 9.5459, test_acc: 69.5625, test_fscore: 69.873, time: 16.17 sec
epoch: 45, train_loss: 8.055, train_acc: 83.6145, train_fscore: 83.4929, test_loss: 9.5504, test_acc: 70.7948, test_fscore: 71.2798, time: 16.15 sec
epoch: 46, train_loss: 8.1061, train_acc: 83.1325, train_fscore: 83.0195, test_loss: 9.5615, test_acc: 68.5767, test_fscore: 68.9052, time: 16.07 sec

*** 新的最优F1分数: 72.0533% (Epoch 47) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 72.2222% (144 样本)
sad: 80.0000% (245 样本)
neutral: 68.4896% (384 样本)
angry: 74.1176% (170 样本)
excited: 73.9130% (299 样本)
frustrated: 66.6667% (381 样本)

每个类别的F1分数:
happy: 61.1765%
sad: 83.2272%
neutral: 71.4674%
angry: 67.7419%
excited: 78.0919%
frustrated: 66.7543%

总体指标:
总体准确率: 71.7190%
加权准确率 (W_ACC, 按测试集分布): 71.7190%
总体F1分数 (不加权宏平均): 71.4099%
手动计算宏平均F1: 71.4099%
加权F1 (W_F1, 按测试集分布): 72.0533%
============================================================
epoch: 47, train_loss: 8.0077, train_acc: 83.7349, train_fscore: 83.6237, test_loss: 9.4236, test_acc: 71.719, test_fscore: 72.0533, time: 16.17 sec
epoch: 48, train_loss: 8.0203, train_acc: 83.3907, train_fscore: 83.3482, test_loss: 9.5657, test_acc: 70.1171, test_fscore: 70.3287, time: 16.01 sec
epoch: 49, train_loss: 8.0532, train_acc: 84.389, train_fscore: 84.264, test_loss: 9.7628, test_acc: 69.3161, test_fscore: 69.5643, time: 16.0 sec
epoch: 50, train_loss: 8.0568, train_acc: 85.2496, train_fscore: 85.1677, test_loss: 9.8482, test_acc: 71.1029, test_fscore: 71.2286, time: 17.8 sec
----------best F-Score: 72.0533
              precision    recall  f1-score   support

           0     0.5306    0.7222    0.6118       144
           1     0.8673    0.8000    0.8323       245
           2     0.7472    0.6849    0.7147       384
           3     0.6238    0.7412    0.6774       170
           4     0.8277    0.7391    0.7809       299
           5     0.6684    0.6667    0.6675       381

    accuracy                         0.7172      1623
   macro avg     0.7108    0.7257    0.7141      1623
weighted avg     0.7295    0.7172    0.7205      1623

[[104   1   5   0  32   2]
 [  4 196  10   3   0  32]
 [ 33  18 263   8   9  53]
 [  0   0   7 126   0  37]
 [ 50   2  24   0 221   2]
 [  5   9  43  65   5 254]]
epoch: 51, train_loss: 8.1608, train_acc: 84.475, train_fscore: 84.3763, test_loss: 9.7298, test_acc: 70.3635, test_fscore: 70.6727, time: 17.02 sec
epoch: 52, train_loss: 8.0853, train_acc: 85.4045, train_fscore: 85.2917, test_loss: 9.7728, test_acc: 69.6858, test_fscore: 70.0847, time: 18.21 sec
epoch: 53, train_loss: 8.1366, train_acc: 85.8348, train_fscore: 85.7388, test_loss: 9.6093, test_acc: 70.4251, test_fscore: 70.9681, time: 18.83 sec
epoch: 54, train_loss: 8.0599, train_acc: 85.8692, train_fscore: 85.8023, test_loss: 9.6783, test_acc: 70.4251, test_fscore: 70.7428, time: 18.65 sec
epoch: 55, train_loss: 7.9429, train_acc: 85.2668, train_fscore: 85.1376, test_loss: 9.673, test_acc: 70.2403, test_fscore: 70.6347, time: 18.22 sec
epoch: 56, train_loss: 7.9708, train_acc: 85.7487, train_fscore: 85.6174, test_loss: 9.6358, test_acc: 70.4251, test_fscore: 70.6771, time: 18.26 sec
epoch: 57, train_loss: 7.8571, train_acc: 85.8348, train_fscore: 85.7357, test_loss: 9.6264, test_acc: 69.3777, test_fscore: 69.845, time: 17.09 sec
epoch: 58, train_loss: 7.8537, train_acc: 86.6609, train_fscore: 86.5501, test_loss: 9.6222, test_acc: 70.8564, test_fscore: 71.097, time: 16.34 sec
epoch: 59, train_loss: 7.8555, train_acc: 86.5749, train_fscore: 86.4912, test_loss: 9.5959, test_acc: 71.719, test_fscore: 71.8406, time: 18.28 sec
epoch: 60, train_loss: 7.7682, train_acc: 86.5921, train_fscore: 86.5032, test_loss: 9.5231, test_acc: 70.1171, test_fscore: 70.4969, time: 18.62 sec
----------best F-Score: 72.0533
              precision    recall  f1-score   support

           0     0.5306    0.7222    0.6118       144
           1     0.8673    0.8000    0.8323       245
           2     0.7472    0.6849    0.7147       384
           3     0.6238    0.7412    0.6774       170
           4     0.8277    0.7391    0.7809       299
           5     0.6684    0.6667    0.6675       381

    accuracy                         0.7172      1623
   macro avg     0.7108    0.7257    0.7141      1623
weighted avg     0.7295    0.7172    0.7205      1623

[[104   1   5   0  32   2]
 [  4 196  10   3   0  32]
 [ 33  18 263   8   9  53]
 [  0   0   7 126   0  37]
 [ 50   2  24   0 221   2]
 [  5   9  43  65   5 254]]
epoch: 61, train_loss: 7.7174, train_acc: 86.9707, train_fscore: 86.8567, test_loss: 9.6996, test_acc: 69.7474, test_fscore: 70.0416, time: 16.97 sec
epoch: 62, train_loss: 7.7266, train_acc: 87.2978, train_fscore: 87.2316, test_loss: 9.6349, test_acc: 69.2545, test_fscore: 69.5916, time: 16.4 sec
epoch: 63, train_loss: 7.7325, train_acc: 86.833, train_fscore: 86.7277, test_loss: 9.7838, test_acc: 71.4726, test_fscore: 71.6066, time: 16.2 sec
epoch: 64, train_loss: 7.7903, train_acc: 87.7453, train_fscore: 87.6702, test_loss: 9.7926, test_acc: 70.1787, test_fscore: 70.3071, time: 16.19 sec
epoch: 65, train_loss: 7.8963, train_acc: 87.0052, train_fscore: 86.8973, test_loss: 9.9599, test_acc: 71.1645, test_fscore: 71.2406, time: 16.01 sec
epoch: 66, train_loss: 7.9456, train_acc: 87.3666, train_fscore: 87.2789, test_loss: 10.0799, test_acc: 70.8564, test_fscore: 71.1718, time: 16.17 sec
epoch: 67, train_loss: 7.9711, train_acc: 88.1756, train_fscore: 88.1017, test_loss: 10.2081, test_acc: 69.9938, test_fscore: 70.1131, time: 16.02 sec
epoch: 68, train_loss: 8.0511, train_acc: 87.6248, train_fscore: 87.5175, test_loss: 9.7925, test_acc: 70.2403, test_fscore: 70.5744, time: 15.99 sec
epoch: 69, train_loss: 7.9603, train_acc: 87.8313, train_fscore: 87.7136, test_loss: 10.0744, test_acc: 69.8706, test_fscore: 70.0574, time: 16.21 sec

*** 新的最优F1分数: 72.3766% (Epoch 70) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 69.4444% (144 样本)
sad: 76.3265% (245 样本)
neutral: 78.9062% (384 样本)
angry: 69.4118% (170 样本)
excited: 73.2441% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 64.9351%
sad: 79.9145%
neutral: 72.3150%
angry: 68.2081%
excited: 77.3852%
frustrated: 68.3333%

总体指标:
总体准确率: 72.2736%
加权准确率 (W_ACC, 按测试集分布): 72.2736%
总体F1分数 (不加权宏平均): 71.8485%
手动计算宏平均F1: 71.8485%
加权F1 (W_F1, 按测试集分布): 72.3766%
============================================================
epoch: 70, train_loss: 7.8883, train_acc: 87.6076, train_fscore: 87.5491, test_loss: 9.9558, test_acc: 72.2736, test_fscore: 72.3766, time: 16.13 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 71, train_loss: 7.8202, train_acc: 87.883, train_fscore: 87.8099, test_loss: 9.708, test_acc: 70.3635, test_fscore: 70.5757, time: 16.23 sec
epoch: 72, train_loss: 7.7311, train_acc: 88.1067, train_fscore: 88.0232, test_loss: 9.7092, test_acc: 69.5009, test_fscore: 69.8606, time: 16.2 sec
epoch: 73, train_loss: 7.6868, train_acc: 87.969, train_fscore: 87.8621, test_loss: 9.7025, test_acc: 70.9797, test_fscore: 71.0229, time: 16.0 sec
epoch: 74, train_loss: 7.6816, train_acc: 88.4165, train_fscore: 88.3243, test_loss: 9.9268, test_acc: 69.9322, test_fscore: 70.0659, time: 16.19 sec
epoch: 75, train_loss: 7.6205, train_acc: 89.191, train_fscore: 89.116, test_loss: 9.6693, test_acc: 71.5342, test_fscore: 71.5877, time: 16.2 sec
epoch: 76, train_loss: 7.6226, train_acc: 89.0361, train_fscore: 88.9562, test_loss: 9.828, test_acc: 70.4868, test_fscore: 70.7822, time: 16.19 sec
epoch: 77, train_loss: 7.5913, train_acc: 88.5198, train_fscore: 88.429, test_loss: 9.8721, test_acc: 70.7332, test_fscore: 70.7636, time: 16.6 sec
epoch: 78, train_loss: 7.5416, train_acc: 89.1222, train_fscore: 89.0251, test_loss: 9.8573, test_acc: 70.5484, test_fscore: 70.6451, time: 16.98 sec
epoch: 79, train_loss: 7.5029, train_acc: 89.5181, train_fscore: 89.4479, test_loss: 9.7915, test_acc: 71.719, test_fscore: 71.8795, time: 17.6 sec
epoch: 80, train_loss: 7.518, train_acc: 89.5697, train_fscore: 89.4798, test_loss: 10.0751, test_acc: 69.008, test_fscore: 69.2128, time: 19.28 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 81, train_loss: 7.4846, train_acc: 89.3632, train_fscore: 89.2739, test_loss: 9.889, test_acc: 71.5342, test_fscore: 71.6192, time: 18.8 sec
epoch: 82, train_loss: 7.4872, train_acc: 89.8107, train_fscore: 89.7236, test_loss: 10.0876, test_acc: 71.1645, test_fscore: 71.2391, time: 17.8 sec
epoch: 83, train_loss: 7.4566, train_acc: 90.568, train_fscore: 90.5118, test_loss: 10.3204, test_acc: 69.8706, test_fscore: 69.9691, time: 17.58 sec
epoch: 84, train_loss: 7.6867, train_acc: 90.0344, train_fscore: 89.9616, test_loss: 10.5217, test_acc: 70.61, test_fscore: 70.6148, time: 17.81 sec
epoch: 85, train_loss: 7.7522, train_acc: 90.3442, train_fscore: 90.2652, test_loss: 10.2131, test_acc: 70.6716, test_fscore: 70.8659, time: 17.98 sec
epoch: 86, train_loss: 7.8974, train_acc: 90.8606, train_fscore: 90.784, test_loss: 10.4641, test_acc: 70.8564, test_fscore: 70.8956, time: 17.81 sec
epoch: 87, train_loss: 7.861, train_acc: 90.4303, train_fscore: 90.3512, test_loss: 10.1513, test_acc: 71.5342, test_fscore: 71.6565, time: 17.6 sec
epoch: 88, train_loss: 7.683, train_acc: 90.9811, train_fscore: 90.9249, test_loss: 10.5105, test_acc: 70.0555, test_fscore: 70.2183, time: 17.59 sec
epoch: 89, train_loss: 7.5949, train_acc: 90.7917, train_fscore: 90.7266, test_loss: 10.2823, test_acc: 70.0555, test_fscore: 70.065, time: 17.77 sec
epoch: 90, train_loss: 7.6009, train_acc: 91.1532, train_fscore: 91.0957, test_loss: 10.4424, test_acc: 69.3777, test_fscore: 69.4478, time: 17.39 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 91, train_loss: 7.5737, train_acc: 91.1188, train_fscore: 91.0505, test_loss: 10.0308, test_acc: 72.0887, test_fscore: 72.1015, time: 18.34 sec
epoch: 92, train_loss: 7.561, train_acc: 91.1704, train_fscore: 91.1064, test_loss: 10.3021, test_acc: 69.5625, test_fscore: 69.5648, time: 18.05 sec
epoch: 93, train_loss: 7.5257, train_acc: 91.222, train_fscore: 91.1519, test_loss: 10.1206, test_acc: 71.2261, test_fscore: 71.347, time: 17.61 sec
epoch: 94, train_loss: 7.5267, train_acc: 91.2737, train_fscore: 91.23, test_loss: 10.1475, test_acc: 70.8564, test_fscore: 70.7949, time: 17.79 sec
epoch: 95, train_loss: 7.4545, train_acc: 91.463, train_fscore: 91.3984, test_loss: 10.1982, test_acc: 70.4251, test_fscore: 70.3636, time: 18.0 sec
epoch: 96, train_loss: 7.4162, train_acc: 91.4802, train_fscore: 91.4099, test_loss: 10.0339, test_acc: 70.7948, test_fscore: 70.8538, time: 18.0 sec
epoch: 97, train_loss: 7.365, train_acc: 91.5146, train_fscore: 91.4582, test_loss: 10.224, test_acc: 70.61, test_fscore: 70.5801, time: 17.8 sec
epoch: 98, train_loss: 7.3622, train_acc: 92.3924, train_fscore: 92.3531, test_loss: 10.013, test_acc: 71.6574, test_fscore: 71.7637, time: 17.38 sec
epoch: 99, train_loss: 7.31, train_acc: 92.0138, train_fscore: 91.9404, test_loss: 10.1944, test_acc: 70.4868, test_fscore: 70.5591, time: 17.79 sec
epoch: 100, train_loss: 7.323, train_acc: 92.358, train_fscore: 92.3079, test_loss: 10.2215, test_acc: 72.0887, test_fscore: 72.1376, time: 17.99 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 101, train_loss: 7.3708, train_acc: 92.3752, train_fscore: 92.3182, test_loss: 10.4384, test_acc: 70.1171, test_fscore: 70.1892, time: 17.8 sec
epoch: 102, train_loss: 7.3271, train_acc: 92.7194, train_fscore: 92.6784, test_loss: 10.3543, test_acc: 71.9039, test_fscore: 71.9689, time: 19.8 sec
epoch: 103, train_loss: 7.4047, train_acc: 92.6334, train_fscore: 92.5875, test_loss: 10.4216, test_acc: 71.2877, test_fscore: 71.4291, time: 17.79 sec
epoch: 104, train_loss: 7.5293, train_acc: 92.7194, train_fscore: 92.6632, test_loss: 10.5291, test_acc: 70.8564, test_fscore: 70.8844, time: 17.79 sec
epoch: 105, train_loss: 7.6914, train_acc: 93.3735, train_fscore: 93.3393, test_loss: 10.4046, test_acc: 70.7332, test_fscore: 70.7527, time: 17.79 sec
epoch: 106, train_loss: 7.5649, train_acc: 92.7194, train_fscore: 92.6686, test_loss: 10.5362, test_acc: 69.809, test_fscore: 69.821, time: 17.61 sec
epoch: 107, train_loss: 7.3968, train_acc: 92.6334, train_fscore: 92.5849, test_loss: 10.5616, test_acc: 71.411, test_fscore: 71.463, time: 17.38 sec
epoch: 108, train_loss: 7.4579, train_acc: 93.0465, train_fscore: 93.0018, test_loss: 10.5317, test_acc: 72.212, test_fscore: 72.2595, time: 18.27 sec
epoch: 109, train_loss: 7.3805, train_acc: 93.012, train_fscore: 92.9704, test_loss: 10.386, test_acc: 71.9655, test_fscore: 71.9158, time: 18.53 sec
epoch: 110, train_loss: 7.3704, train_acc: 93.167, train_fscore: 93.1275, test_loss: 10.2064, test_acc: 71.411, test_fscore: 71.4147, time: 17.81 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 111, train_loss: 7.3206, train_acc: 93.253, train_fscore: 93.2056, test_loss: 10.4592, test_acc: 69.9938, test_fscore: 70.028, time: 18.24 sec
epoch: 112, train_loss: 7.2862, train_acc: 93.6145, train_fscore: 93.5812, test_loss: 10.2289, test_acc: 71.9039, test_fscore: 71.9245, time: 17.75 sec
epoch: 113, train_loss: 7.2837, train_acc: 93.6661, train_fscore: 93.6237, test_loss: 10.4037, test_acc: 70.9181, test_fscore: 70.956, time: 17.75 sec
epoch: 114, train_loss: 7.2386, train_acc: 94.0103, train_fscore: 93.98, test_loss: 10.3684, test_acc: 71.1645, test_fscore: 71.2959, time: 18.37 sec
epoch: 115, train_loss: 7.2141, train_acc: 94.0448, train_fscore: 93.9994, test_loss: 10.5031, test_acc: 71.2877, test_fscore: 71.3427, time: 17.9 sec
epoch: 116, train_loss: 7.1813, train_acc: 93.8554, train_fscore: 93.8092, test_loss: 10.4291, test_acc: 70.5484, test_fscore: 70.5478, time: 17.89 sec
epoch: 117, train_loss: 7.1784, train_acc: 94.062, train_fscore: 94.0326, test_loss: 10.465, test_acc: 70.3019, test_fscore: 70.3824, time: 17.08 sec
epoch: 118, train_loss: 7.1475, train_acc: 94.1308, train_fscore: 94.0877, test_loss: 10.4123, test_acc: 71.1029, test_fscore: 71.1396, time: 17.15 sec
epoch: 119, train_loss: 7.268, train_acc: 94.2513, train_fscore: 94.2089, test_loss: 10.9507, test_acc: 71.411, test_fscore: 71.584, time: 18.57 sec
epoch: 120, train_loss: 7.5289, train_acc: 94.3718, train_fscore: 94.3316, test_loss: 10.9021, test_acc: 71.2877, test_fscore: 71.3288, time: 18.11 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 121, train_loss: 7.4114, train_acc: 93.9931, train_fscore: 93.9588, test_loss: 10.7091, test_acc: 71.5958, test_fscore: 71.7721, time: 18.52 sec
epoch: 122, train_loss: 7.4083, train_acc: 94.4578, train_fscore: 94.4319, test_loss: 10.6934, test_acc: 71.6574, test_fscore: 71.7211, time: 18.11 sec
epoch: 123, train_loss: 7.3004, train_acc: 93.9931, train_fscore: 93.9572, test_loss: 10.7298, test_acc: 70.61, test_fscore: 70.6439, time: 17.27 sec
epoch: 124, train_loss: 7.2936, train_acc: 94.148, train_fscore: 94.1144, test_loss: 10.6464, test_acc: 70.4868, test_fscore: 70.4645, time: 17.72 sec
epoch: 125, train_loss: 7.218, train_acc: 94.6127, train_fscore: 94.5816, test_loss: 10.4973, test_acc: 70.8564, test_fscore: 70.8964, time: 17.07 sec
epoch: 126, train_loss: 7.1826, train_acc: 94.6644, train_fscore: 94.6387, test_loss: 10.664, test_acc: 70.8564, test_fscore: 70.9092, time: 17.25 sec
epoch: 127, train_loss: 7.1963, train_acc: 94.9398, train_fscore: 94.9082, test_loss: 10.6751, test_acc: 70.1171, test_fscore: 70.0409, time: 17.96 sec
epoch: 128, train_loss: 7.181, train_acc: 94.6472, train_fscore: 94.6176, test_loss: 10.5504, test_acc: 71.1645, test_fscore: 71.1706, time: 17.57 sec
epoch: 129, train_loss: 7.1642, train_acc: 94.9914, train_fscore: 94.9624, test_loss: 10.5634, test_acc: 70.3635, test_fscore: 70.5121, time: 17.63 sec
epoch: 130, train_loss: 7.1281, train_acc: 95.284, train_fscore: 95.2617, test_loss: 10.5407, test_acc: 71.4726, test_fscore: 71.4533, time: 19.26 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 131, train_loss: 7.0739, train_acc: 95.2496, train_fscore: 95.2306, test_loss: 10.6114, test_acc: 69.6242, test_fscore: 69.659, time: 18.48 sec
epoch: 132, train_loss: 7.0606, train_acc: 94.9225, train_fscore: 94.896, test_loss: 10.5441, test_acc: 70.9181, test_fscore: 70.9468, time: 17.59 sec
epoch: 133, train_loss: 7.0935, train_acc: 95.2668, train_fscore: 95.2458, test_loss: 10.5394, test_acc: 70.6716, test_fscore: 70.6964, time: 17.79 sec
epoch: 134, train_loss: 7.1872, train_acc: 95.2668, train_fscore: 95.2421, test_loss: 10.8152, test_acc: 69.5625, test_fscore: 69.6673, time: 17.61 sec
epoch: 135, train_loss: 7.207, train_acc: 95.0258, train_fscore: 94.9975, test_loss: 10.8203, test_acc: 69.7474, test_fscore: 69.7752, time: 17.61 sec
epoch: 136, train_loss: 7.2505, train_acc: 95.6282, train_fscore: 95.6092, test_loss: 10.813, test_acc: 71.1029, test_fscore: 71.2043, time: 17.59 sec
epoch: 137, train_loss: 7.409, train_acc: 95.284, train_fscore: 95.258, test_loss: 11.0433, test_acc: 69.8706, test_fscore: 69.9276, time: 17.78 sec
epoch: 138, train_loss: 7.247, train_acc: 95.2151, train_fscore: 95.1851, test_loss: 10.8979, test_acc: 71.0413, test_fscore: 71.0339, time: 17.79 sec
epoch: 139, train_loss: 7.2451, train_acc: 95.4045, train_fscore: 95.386, test_loss: 10.8686, test_acc: 69.809, test_fscore: 69.8691, time: 17.78 sec
epoch: 140, train_loss: 7.1829, train_acc: 95.4733, train_fscore: 95.4437, test_loss: 10.7311, test_acc: 70.8564, test_fscore: 70.8999, time: 17.41 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
epoch: 141, train_loss: 7.1418, train_acc: 95.4561, train_fscore: 95.4379, test_loss: 10.8317, test_acc: 70.7332, test_fscore: 70.7538, time: 17.59 sec
epoch: 142, train_loss: 7.0813, train_acc: 95.5077, train_fscore: 95.4856, test_loss: 10.8389, test_acc: 70.2403, test_fscore: 70.3989, time: 17.6 sec
epoch: 143, train_loss: 7.097, train_acc: 95.2324, train_fscore: 95.2188, test_loss: 10.8591, test_acc: 71.0413, test_fscore: 71.066, time: 17.6 sec
epoch: 144, train_loss: 7.1344, train_acc: 95.4389, train_fscore: 95.4186, test_loss: 10.7411, test_acc: 70.6716, test_fscore: 70.795, time: 17.43 sec
epoch: 145, train_loss: 7.0391, train_acc: 96.1274, train_fscore: 96.1167, test_loss: 10.7388, test_acc: 70.3019, test_fscore: 70.2956, time: 17.55 sec
epoch: 146, train_loss: 7.0274, train_acc: 95.3873, train_fscore: 95.3654, test_loss: 11.01, test_acc: 69.7474, test_fscore: 69.7577, time: 17.79 sec
epoch: 147, train_loss: 7.021, train_acc: 95.6282, train_fscore: 95.6046, test_loss: 10.7357, test_acc: 71.5342, test_fscore: 71.6838, time: 17.92 sec
epoch: 148, train_loss: 6.9988, train_acc: 96.0241, train_fscore: 95.9998, test_loss: 10.9652, test_acc: 69.7474, test_fscore: 69.7647, time: 17.93 sec
epoch: 149, train_loss: 6.9591, train_acc: 95.8348, train_fscore: 95.8113, test_loss: 11.0983, test_acc: 70.6716, test_fscore: 70.7431, time: 18.39 sec
epoch: 150, train_loss: 6.9776, train_acc: 96.5921, train_fscore: 96.5803, test_loss: 11.107, test_acc: 70.61, test_fscore: 70.6532, time: 17.7 sec
----------best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]
Test performance..
Best F-Score: 72.3766
              precision    recall  f1-score   support

           0     0.6098    0.6944    0.6494       144
           1     0.8386    0.7633    0.7991       245
           2     0.6674    0.7891    0.7232       384
           3     0.6705    0.6941    0.6821       170
           4     0.8202    0.7324    0.7739       299
           5     0.7257    0.6457    0.6833       381

    accuracy                         0.7227      1623
   macro avg     0.7220    0.7198    0.7185      1623
weighted avg     0.7303    0.7227    0.7238      1623

[[100   2   6   0  35   1]
 [  2 187  29   3   0  24]
 [ 24  20 303   4   6  27]
 [  0   1  11 118   0  40]
 [ 37   2  40   0 219   1]
 [  1  11  65  51   7 246]]

==================================================
DETAILED EVALUATION WITH WEIGHTS
==================================================

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 69.4444% (144 样本)
sad: 76.3265% (245 样本)
neutral: 78.9062% (384 样本)
angry: 69.4118% (170 样本)
excited: 73.2441% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 64.9351%
sad: 79.9145%
neutral: 72.3150%
angry: 68.2081%
excited: 77.3852%
frustrated: 68.3333%

总体指标:
总体准确率: 72.2736%
加权准确率 (W_ACC, 按测试集分布): 72.2736%
总体F1分数 (不加权宏平均): 71.8485%
手动计算宏平均F1: 71.8485%
加权F1 (W_F1, 按测试集分布): 72.3766%
============================================================

进程已结束，退出代码为 0
