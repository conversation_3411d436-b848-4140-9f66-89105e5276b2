D:\Anaconda\envs\ljp\python.exe "E:\Pycharm WorkingSpace\MMERC\SDT-main\train.py"
Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=3, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 3
total parameters: 79687704
training parameters: 79687704

*** 新的最优F1分数: 23.1950% (Epoch 1) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 0.0000% (144 样本)
sad: 46.5306% (245 样本)
neutral: 44.5312% (384 样本)
angry: 7.6471% (170 样本)
excited: 0.3344% (299 样本)
frustrated: 38.3202% (381 样本)

每个类别的F1分数:
happy: 0.0000%
sad: 49.2441%
neutral: 26.9929%
angry: 12.3223%
excited: 0.6667%
frustrated: 33.9141%

总体指标:
总体准确率: 27.4184%
加权准确率 (W_ACC, 按测试集分布): 27.4184%
总体F1分数 (不加权宏平均): 20.5233%
手动计算宏平均F1: 20.5233%
加权F1 (W_F1, 按测试集分布): 23.1950%
============================================================
epoch: 1, train_loss: 20.5364, train_acc: 20.0516, train_fscore: 16.273, test_loss: 20.8511, test_acc: 27.4184, test_fscore: 23.195, time: 16.69 sec

*** 新的最优F1分数: 35.0706% (Epoch 2) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 11.8056% (144 样本)
sad: 88.1633% (245 样本)
neutral: 0.0000% (384 样本)
angry: 60.5882% (170 样本)
excited: 44.1472% (299 样本)
frustrated: 48.0315% (381 样本)

每个类别的F1分数:
happy: 14.0496%
sad: 66.1562%
neutral: 0.0000%
angry: 30.9309%
excited: 55.0000%
frustrated: 44.5798%

总体指标:
总体准确率: 40.1109%
加权准确率 (W_ACC, 按测试集分布): 40.1109%
总体F1分数 (不加权宏平均): 35.1194%
手动计算宏平均F1: 35.1194%
加权F1 (W_F1, 按测试集分布): 35.0706%
============================================================
epoch: 2, train_loss: 20.0005, train_acc: 35.7831, train_fscore: 34.4415, test_loss: 20.7987, test_acc: 40.1109, test_fscore: 35.0706, time: 16.16 sec

*** 新的最优F1分数: 35.6201% (Epoch 3) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 0.6944% (144 样本)
sad: 89.7959% (245 样本)
neutral: 15.3646% (384 样本)
angry: 90.5882% (170 样本)
excited: 85.2843% (299 样本)
frustrated: 6.2992% (381 样本)

每个类别的F1分数:
happy: 1.3423%
sad: 70.2875%
neutral: 23.6948%
angry: 38.6449%
excited: 68.5484%
frustrated: 11.1111%

总体指标:
总体准确率: 43.9310%
加权准确率 (W_ACC, 按测试集分布): 43.9310%
总体F1分数 (不加权宏平均): 35.6048%
手动计算宏平均F1: 35.6048%
加权F1 (W_F1, 按测试集分布): 35.6201%
============================================================
epoch: 3, train_loss: 19.3178, train_acc: 42.3236, train_fscore: 34.1415, test_loss: 19.5675, test_acc: 43.931, test_fscore: 35.6201, time: 15.95 sec

*** 新的最优F1分数: 52.8396% (Epoch 4) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 15.9722% (144 样本)
sad: 77.5510% (245 样本)
neutral: 23.4375% (384 样本)
angry: 80.5882% (170 样本)
excited: 87.6254% (299 样本)
frustrated: 53.2808% (381 样本)

每个类别的F1分数:
happy: 18.4000%
sad: 73.0769%
neutral: 32.5497%
angry: 62.1315%
excited: 74.5377%
frustrated: 52.1181%

总体指标:
总体准确率: 55.7609%
加权准确率 (W_ACC, 按测试集分布): 55.7609%
总体F1分数 (不加权宏平均): 52.1357%
手动计算宏平均F1: 52.1357%
加权F1 (W_F1, 按测试集分布): 52.8396%
============================================================
epoch: 4, train_loss: 18.2529, train_acc: 52.4613, train_fscore: 46.5156, test_loss: 18.7985, test_acc: 55.7609, test_fscore: 52.8396, time: 15.84 sec

*** 新的最优F1分数: 57.3925% (Epoch 5) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.1667% (144 样本)
sad: 71.8367% (245 样本)
neutral: 52.8646% (384 样本)
angry: 82.3529% (170 样本)
excited: 64.5485% (299 样本)
frustrated: 36.2205% (381 样本)

每个类别的F1分数:
happy: 38.2353%
sad: 71.1111%
neutral: 53.3509%
angry: 62.2222%
excited: 71.7472%
frustrated: 46.4646%

总体指标:
总体准确率: 57.1781%
加权准确率 (W_ACC, 按测试集分布): 57.1781%
总体F1分数 (不加权宏平均): 57.1886%
手动计算宏平均F1: 57.1886%
加权F1 (W_F1, 按测试集分布): 57.3925%
============================================================
epoch: 5, train_loss: 18.0307, train_acc: 60.4991, train_fscore: 58.0938, test_loss: 18.0804, test_acc: 57.1781, test_fscore: 57.3925, time: 15.88 sec

*** 新的最优F1分数: 59.2344% (Epoch 6) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 41.6667% (144 样本)
sad: 71.0204% (245 样本)
neutral: 50.5208% (384 样本)
angry: 87.0588% (170 样本)
excited: 75.2508% (299 样本)
frustrated: 41.9948% (381 样本)

每个类别的F1分数:
happy: 34.2857%
sad: 74.3590%
neutral: 56.5598%
angry: 61.6667%
excited: 74.1351%
frustrated: 48.8550%

总体指标:
总体准确率: 59.2113%
加权准确率 (W_ACC, 按测试集分布): 59.2113%
总体F1分数 (不加权宏平均): 58.3102%
手动计算宏平均F1: 58.3102%
加权F1 (W_F1, 按测试集分布): 59.2344%
============================================================
epoch: 6, train_loss: 17.7652, train_acc: 60.5508, train_fscore: 59.4745, test_loss: 19.5312, test_acc: 59.2113, test_fscore: 59.2344, time: 15.76 sec

*** 新的最优F1分数: 64.1895% (Epoch 7) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 38.8889% (144 样本)
sad: 77.5510% (245 样本)
neutral: 56.2500% (384 样本)
angry: 65.2941% (170 样本)
excited: 69.8997% (299 样本)
frustrated: 67.4541% (381 样本)

每个类别的F1分数:
happy: 36.7213%
sad: 76.6129%
neutral: 57.4468%
angry: 67.4772%
excited: 73.3333%
frustrated: 64.7355%

总体指标:
总体准确率: 64.0173%
加权准确率 (W_ACC, 按测试集分布): 64.0173%
总体F1分数 (不加权宏平均): 62.7212%
手动计算宏平均F1: 62.7212%
加权F1 (W_F1, 按测试集分布): 64.1895%
============================================================
epoch: 7, train_loss: 18.9915, train_acc: 65.9208, train_fscore: 64.57, test_loss: 20.4596, test_acc: 64.0173, test_fscore: 64.1895, time: 15.71 sec
epoch: 8, train_loss: 20.2353, train_acc: 69.5181, train_fscore: 69.1041, test_loss: 22.1813, test_acc: 59.2113, test_fscore: 60.1145, time: 15.82 sec

*** 新的最优F1分数: 65.9985% (Epoch 9) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 48.6111% (144 样本)
sad: 71.0204% (245 样本)
neutral: 60.6771% (384 样本)
angry: 70.0000% (170 样本)
excited: 73.2441% (299 样本)
frustrated: 65.3543% (381 样本)

每个类别的F1分数:
happy: 41.7910%
sad: 75.1620%
neutral: 62.8032%
angry: 67.4221%
excited: 75.1286%
frustrated: 64.6753%

总体指标:
总体准确率: 65.5576%
加权准确率 (W_ACC, 按测试集分布): 65.5576%
总体F1分数 (不加权宏平均): 64.4971%
手动计算宏平均F1: 64.4971%
加权F1 (W_F1, 按测试集分布): 65.9985%
============================================================
epoch: 9, train_loss: 19.8746, train_acc: 67.6592, train_fscore: 67.36, test_loss: 22.6326, test_acc: 65.5576, test_fscore: 65.9985, time: 15.88 sec
epoch: 10, train_loss: 19.8398, train_acc: 68.296, train_fscore: 67.916, test_loss: 19.6978, test_acc: 64.2021, test_fscore: 64.6439, time: 15.75 sec
----------best F-Score: 65.9985
              precision    recall  f1-score   support

           0     0.3665    0.4861    0.4179       144
           1     0.7982    0.7102    0.7516       245
           2     0.6508    0.6068    0.6280       384
           3     0.6503    0.7000    0.6742       170
           4     0.7711    0.7324    0.7513       299
           5     0.6401    0.6535    0.6468       381

    accuracy                         0.6556      1623
   macro avg     0.6462    0.6482    0.6450      1623
weighted avg     0.6674    0.6556    0.6600      1623

[[ 70   7  13   0  51   3]
 [ 11 174  27   2   0  31]
 [ 49  21 233  18   7  56]
 [  0   0   4 119   0  47]
 [ 57   0  20   0 219   3]
 [  4  16  61  44   7 249]]
epoch: 11, train_loss: 19.4943, train_acc: 69.8623, train_fscore: 69.683, test_loss: 20.26, test_acc: 64.3253, test_fscore: 65.2828, time: 15.81 sec
epoch: 12, train_loss: 18.8944, train_acc: 70.1549, train_fscore: 69.6905, test_loss: 19.8344, test_acc: 64.2021, test_fscore: 65.1464, time: 15.98 sec

*** 新的最优F1分数: 66.0292% (Epoch 13) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.1667% (144 样本)
sad: 71.8367% (245 样本)
neutral: 56.7708% (384 样本)
angry: 83.5294% (170 样本)
excited: 82.2742% (299 样本)
frustrated: 55.1181% (381 样本)

每个类别的F1分数:
happy: 48.1481%
sad: 76.0259%
neutral: 63.0058%
angry: 64.2534%
excited: 80.5237%
frustrated: 58.8235%

总体指标:
总体准确率: 65.9273%
加权准确率 (W_ACC, 按测试集分布): 65.9273%
总体F1分数 (不加权宏平均): 65.1301%
手动计算宏平均F1: 65.1301%
加权F1 (W_F1, 按测试集分布): 66.0292%
============================================================
epoch: 13, train_loss: 18.8653, train_acc: 70.4647, train_fscore: 70.5237, test_loss: 19.79, test_acc: 65.9273, test_fscore: 66.0292, time: 15.97 sec
epoch: 14, train_loss: 18.6506, train_acc: 71.8244, train_fscore: 71.599, test_loss: 19.3507, test_acc: 64.2021, test_fscore: 64.8753, time: 16.04 sec

*** 新的最优F1分数: 66.3282% (Epoch 15) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 69.4444% (144 样本)
sad: 69.7959% (245 样本)
neutral: 61.7188% (384 样本)
angry: 72.3529% (170 样本)
excited: 58.8629% (299 样本)
frustrated: 67.1916% (381 样本)

每个类别的F1分数:
happy: 48.7805%
sad: 76.1693%
neutral: 63.9676%
angry: 67.7686%
excited: 69.1552%
frustrated: 66.1499%

总体指标:
总体准确率: 65.4960%
加权准确率 (W_ACC, 按测试集分布): 65.4960%
总体F1分数 (不加权宏平均): 65.3318%
手动计算宏平均F1: 65.3318%
加权F1 (W_F1, 按测试集分布): 66.3282%
============================================================
epoch: 15, train_loss: 18.1274, train_acc: 71.9105, train_fscore: 71.4748, test_loss: 19.128, test_acc: 65.496, test_fscore: 66.3282, time: 15.99 sec

*** 新的最优F1分数: 66.9699% (Epoch 16) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 59.0278% (144 样本)
sad: 82.4490% (245 样本)
neutral: 57.8125% (384 样本)
angry: 81.7647% (170 样本)
excited: 78.2609% (299 样本)
frustrated: 53.2808% (381 样本)

每个类别的F1分数:
happy: 50.5952%
sad: 80.4781%
neutral: 65.8754%
angry: 63.6156%
excited: 79.0541%
frustrated: 57.5887%

总体指标:
总体准确率: 66.8515%
加权准确率 (W_ACC, 按测试集分布): 66.8515%
总体F1分数 (不加权宏平均): 66.2012%
手动计算宏平均F1: 66.2012%
加权F1 (W_F1, 按测试集分布): 66.9699%
============================================================
epoch: 16, train_loss: 17.7364, train_acc: 72.7711, train_fscore: 72.7758, test_loss: 18.3185, test_acc: 66.8515, test_fscore: 66.9699, time: 16.02 sec
epoch: 17, train_loss: 16.6829, train_acc: 72.3064, train_fscore: 72.0553, test_loss: 17.398, test_acc: 65.0031, test_fscore: 65.5427, time: 15.97 sec

*** 新的最优F1分数: 67.6469% (Epoch 18) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 66.6667% (144 样本)
sad: 75.9184% (245 样本)
neutral: 64.5833% (384 样本)
angry: 74.1176% (170 样本)
excited: 71.2375% (299 样本)
frustrated: 56.9554% (381 样本)

每个类别的F1分数:
happy: 47.0588%
sad: 78.1513%
neutral: 67.3913%
angry: 69.6133%
excited: 74.2160%
frustrated: 62.8986%

总体指标:
总体准确率: 66.9131%
加权准确率 (W_ACC, 按测试集分布): 66.9131%
总体F1分数 (不加权宏平均): 66.5549%
手动计算宏平均F1: 66.5549%
加权F1 (W_F1, 按测试集分布): 67.6469%
============================================================
epoch: 18, train_loss: 15.1867, train_acc: 73.9759, train_fscore: 73.7184, test_loss: 16.0219, test_acc: 66.9131, test_fscore: 67.6469, time: 16.0 sec
epoch: 19, train_loss: 13.7088, train_acc: 73.58, train_fscore: 73.441, test_loss: 15.2619, test_acc: 64.8182, test_fscore: 65.408, time: 15.99 sec

*** 新的最优F1分数: 67.8470% (Epoch 20) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.8889% (144 样本)
sad: 76.3265% (245 样本)
neutral: 69.0104% (384 样本)
angry: 75.8824% (170 样本)
excited: 68.8963% (299 样本)
frustrated: 56.6929% (381 样本)

每个类别的F1分数:
happy: 50.8287%
sad: 77.4327%
neutral: 68.1234%
angry: 67.7165%
excited: 74.2342%
frustrated: 62.8821%

总体指标:
总体准确率: 67.4677%
加权准确率 (W_ACC, 按测试集分布): 67.4677%
总体F1分数 (不加权宏平均): 66.8696%
手动计算宏平均F1: 66.8696%
加权F1 (W_F1, 按测试集分布): 67.8470%
============================================================
epoch: 20, train_loss: 13.2477, train_acc: 74.062, train_fscore: 73.868, test_loss: 18.6688, test_acc: 67.4677, test_fscore: 67.847, time: 15.81 sec
----------best F-Score: 67.847
              precision    recall  f1-score   support

           0     0.4220    0.6389    0.5083       144
           1     0.7857    0.7633    0.7743       245
           2     0.6726    0.6901    0.6812       384
           3     0.6114    0.7588    0.6772       170
           4     0.8047    0.6890    0.7423       299
           5     0.7059    0.5669    0.6288       381

    accuracy                         0.6747      1623
   macro avg     0.6670    0.6845    0.6687      1623
weighted avg     0.6932    0.6747    0.6785      1623

[[ 92   4  10   0  37   1]
 [ 11 187  24   2   0  21]
 [ 43  23 265  15   7  31]
 [  0   1   4 129   0  36]
 [ 67   0  25   0 206   1]
 [  5  23  66  65   6 216]]
epoch: 21, train_loss: 15.628, train_acc: 75.6454, train_fscore: 75.3494, test_loss: 22.415, test_acc: 64.0789, test_fscore: 64.872, time: 15.99 sec
epoch: 22, train_loss: 20.2378, train_acc: 74.4234, train_fscore: 74.3281, test_loss: 25.7302, test_acc: 67.5293, test_fscore: 67.7073, time: 16.0 sec
epoch: 23, train_loss: 20.7907, train_acc: 75.8176, train_fscore: 75.5804, test_loss: 23.6292, test_acc: 64.7566, test_fscore: 65.6527, time: 16.01 sec

*** 新的最优F1分数: 69.5997% (Epoch 24) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.0556% (144 样本)
sad: 75.1020% (245 样本)
neutral: 67.7083% (384 样本)
angry: 71.7647% (170 样本)
excited: 66.5552% (299 样本)
frustrated: 67.9790% (381 样本)

每个类别的F1分数:
happy: 55.5241%
sad: 79.6537%
neutral: 68.1520%
angry: 67.9666%
excited: 74.1155%
frustrated: 67.0984%

总体指标:
总体准确率: 69.1312%
加权准确率 (W_ACC, 按测试集分布): 69.1312%
总体F1分数 (不加权宏平均): 68.7517%
手动计算宏平均F1: 68.7517%
加权F1 (W_F1, 按测试集分布): 69.5997%
============================================================
epoch: 24, train_loss: 21.1952, train_acc: 75.2151, train_fscore: 74.9846, test_loss: 18.9129, test_acc: 69.1312, test_fscore: 69.5997, time: 15.98 sec
epoch: 25, train_loss: 18.611, train_acc: 77.4182, train_fscore: 77.353, test_loss: 20.3613, test_acc: 63.6476, test_fscore: 63.9265, time: 16.0 sec

*** 新的最优F1分数: 69.8413% (Epoch 26) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 58.3333% (144 样本)
sad: 73.8776% (245 样本)
neutral: 65.3646% (384 样本)
angry: 74.1176% (170 样本)
excited: 77.2575% (299 样本)
frustrated: 67.1916% (381 样本)

每个类别的F1分数:
happy: 53.5032%
sad: 79.9117%
neutral: 67.6550%
angry: 68.1081%
excited: 78.1726%
frustrated: 65.9794%

总体指标:
总体准确率: 69.5625%
加权准确率 (W_ACC, 按测试集分布): 69.5625%
总体F1分数 (不加权宏平均): 68.8883%
手动计算宏平均F1: 68.8883%
加权F1 (W_F1, 按测试集分布): 69.8413%
============================================================
epoch: 26, train_loss: 18.8507, train_acc: 74.8881, train_fscore: 74.5461, test_loss: 19.4897, test_acc: 69.5625, test_fscore: 69.8413, time: 16.41 sec
epoch: 27, train_loss: 17.9434, train_acc: 77.3838, train_fscore: 77.2527, test_loss: 19.3501, test_acc: 65.6192, test_fscore: 66.3303, time: 16.4 sec
epoch: 28, train_loss: 18.0535, train_acc: 76.9707, train_fscore: 76.724, test_loss: 19.2522, test_acc: 66.7899, test_fscore: 67.442, time: 15.98 sec
epoch: 29, train_loss: 17.6924, train_acc: 77.6936, train_fscore: 77.6105, test_loss: 18.7494, test_acc: 67.7141, test_fscore: 68.1093, time: 16.0 sec
epoch: 30, train_loss: 17.159, train_acc: 78.1583, train_fscore: 78.0912, test_loss: 18.5192, test_acc: 67.8373, test_fscore: 67.9184, time: 16.0 sec
----------best F-Score: 69.8413
              precision    recall  f1-score   support

           0     0.4941    0.5833    0.5350       144
           1     0.8702    0.7388    0.7991       245
           2     0.7011    0.6536    0.6765       384
           3     0.6300    0.7412    0.6811       170
           4     0.7911    0.7726    0.7817       299
           5     0.6481    0.6719    0.6598       381

    accuracy                         0.6956      1623
   macro avg     0.6891    0.6936    0.6889      1623
weighted avg     0.7050    0.6956    0.6984      1623

[[ 84   2  10   0  46   2]
 [  7 181  25   2   0  30]
 [ 34  14 251  11  10  64]
 [  0   0   3 126   0  41]
 [ 44   0  22   0 231   2]
 [  1  11  47  61   5 256]]
epoch: 31, train_loss: 16.7575, train_acc: 77.4871, train_fscore: 77.237, test_loss: 18.0685, test_acc: 65.9889, test_fscore: 66.7224, time: 16.0 sec
epoch: 32, train_loss: 15.9148, train_acc: 78.2444, train_fscore: 78.1688, test_loss: 16.9049, test_acc: 67.2828, test_fscore: 67.8022, time: 15.97 sec
epoch: 33, train_loss: 14.6771, train_acc: 77.074, train_fscore: 76.9078, test_loss: 15.8856, test_acc: 68.8848, test_fscore: 69.2614, time: 16.0 sec
epoch: 34, train_loss: 12.9501, train_acc: 79.0189, train_fscore: 78.8979, test_loss: 14.2513, test_acc: 67.2212, test_fscore: 67.6421, time: 16.0 sec
epoch: 35, train_loss: 10.9984, train_acc: 78.537, train_fscore: 78.3497, test_loss: 12.6595, test_acc: 68.207, test_fscore: 68.7108, time: 15.98 sec
epoch: 36, train_loss: 9.8397, train_acc: 79.0706, train_fscore: 79.0407, test_loss: 12.3607, test_acc: 68.0222, test_fscore: 68.5059, time: 15.99 sec
epoch: 37, train_loss: 9.397, train_acc: 80.1377, train_fscore: 79.9246, test_loss: 11.3805, test_acc: 68.8232, test_fscore: 69.423, time: 16.0 sec
epoch: 38, train_loss: 9.3191, train_acc: 79.5181, train_fscore: 79.3941, test_loss: 11.2369, test_acc: 68.7616, test_fscore: 68.8443, time: 16.0 sec
epoch: 39, train_loss: 8.7723, train_acc: 80.6024, train_fscore: 80.457, test_loss: 11.2769, test_acc: 68.2686, test_fscore: 69.0578, time: 15.98 sec
epoch: 40, train_loss: 8.7445, train_acc: 79.3632, train_fscore: 79.1927, test_loss: 12.3155, test_acc: 67.5909, test_fscore: 67.9234, time: 16.0 sec
----------best F-Score: 69.8413
              precision    recall  f1-score   support

           0     0.5048    0.7292    0.5966       144
           1     0.8738    0.7347    0.7982       245
           2     0.6736    0.7578    0.7132       384
           3     0.5455    0.8118    0.6525       170
           4     0.8333    0.7191    0.7720       299
           5     0.7030    0.4908    0.5781       381

    accuracy                         0.6876      1623
   macro avg     0.6890    0.7072    0.6851      1623
weighted avg     0.7118    0.6876    0.6884      1623

[[105   1   4   0  33   1]
 [  5 180  31   3   1  25]
 [ 40  12 291  12   3  26]
 [  0   1   5 138   0  26]
 [ 52   0  31   0 215   1]
 [  6  12  70 100   6 187]]
epoch: 41, train_loss: 9.0714, train_acc: 80.2065, train_fscore: 80.1516, test_loss: 11.4653, test_acc: 68.8848, test_fscore: 69.2396, time: 15.98 sec
epoch: 42, train_loss: 9.0486, train_acc: 80.6885, train_fscore: 80.5339, test_loss: 14.4812, test_acc: 68.7616, test_fscore: 69.2575, time: 16.01 sec

*** 新的最优F1分数: 70.0047% (Epoch 43) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 79.8611% (144 样本)
sad: 76.7347% (245 样本)
neutral: 65.1042% (384 样本)
angry: 75.8824% (170 样本)
excited: 70.9030% (299 样本)
frustrated: 61.1549% (381 样本)

每个类别的F1分数:
happy: 57.0720%
sad: 80.3419%
neutral: 68.9655%
angry: 68.0739%
excited: 76.8116%
frustrated: 64.8122%

总体指标:
总体准确率: 69.4393%
加权准确率 (W_ACC, 按测试集分布): 69.4393%
总体F1分数 (不加权宏平均): 69.3462%
手动计算宏平均F1: 69.3462%
加权F1 (W_F1, 按测试集分布): 70.0047%
============================================================
epoch: 43, train_loss: 9.7329, train_acc: 80.9811, train_fscore: 80.9449, test_loss: 11.074, test_acc: 69.4393, test_fscore: 70.0047, time: 16.15 sec

*** 新的最优F1分数: 70.9762% (Epoch 44) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 67.3611% (144 样本)
sad: 75.5102% (245 样本)
neutral: 69.2708% (384 样本)
angry: 77.6471% (170 样本)
excited: 77.5920% (299 样本)
frustrated: 61.9423% (381 样本)

每个类别的F1分数:
happy: 60.2484%
sad: 80.4348%
neutral: 70.4636%
angry: 67.6923%
excited: 79.8623%
frustrated: 63.9566%

总体指标:
总体准确率: 70.7332%
加权准确率 (W_ACC, 按测试集分布): 70.7332%
总体F1分数 (不加权宏平均): 70.4430%
手动计算宏平均F1: 70.4430%
加权F1 (W_F1, 按测试集分布): 70.9762%
============================================================
epoch: 44, train_loss: 9.8276, train_acc: 80.9983, train_fscore: 80.8312, test_loss: 16.8442, test_acc: 70.7332, test_fscore: 70.9762, time: 16.04 sec
epoch: 45, train_loss: 10.744, train_acc: 81.5318, train_fscore: 81.4494, test_loss: 11.135, test_acc: 67.5293, test_fscore: 67.922, time: 15.99 sec
epoch: 46, train_loss: 9.8664, train_acc: 81.463, train_fscore: 81.3755, test_loss: 17.2436, test_acc: 69.3777, test_fscore: 69.93, time: 16.0 sec
epoch: 47, train_loss: 11.6074, train_acc: 81.2909, train_fscore: 81.1307, test_loss: 11.9305, test_acc: 69.5009, test_fscore: 70.1962, time: 16.32 sec
epoch: 48, train_loss: 9.7815, train_acc: 81.6867, train_fscore: 81.5797, test_loss: 13.9171, test_acc: 69.5625, test_fscore: 70.1377, time: 16.07 sec
epoch: 49, train_loss: 10.4784, train_acc: 81.6867, train_fscore: 81.5802, test_loss: 13.4951, test_acc: 69.8706, test_fscore: 70.2761, time: 15.99 sec
epoch: 50, train_loss: 9.7789, train_acc: 81.5835, train_fscore: 81.5212, test_loss: 11.1095, test_acc: 69.6858, test_fscore: 69.7294, time: 15.99 sec
----------best F-Score: 70.9762
              precision    recall  f1-score   support

           0     0.5449    0.6736    0.6025       144
           1     0.8605    0.7551    0.8043       245
           2     0.7170    0.6927    0.7046       384
           3     0.6000    0.7765    0.6769       170
           4     0.8227    0.7759    0.7986       299
           5     0.6611    0.6194    0.6396       381

    accuracy                         0.7073      1623
   macro avg     0.7010    0.7155    0.7044      1623
weighted avg     0.7175    0.7073    0.7098      1623

[[ 97   2   4   0  39   2]
 [  3 185  25   2   0  30]
 [ 34  17 266  10   5  52]
 [  0   0   3 132   0  35]
 [ 41   0  24   0 232   2]
 [  3  11  49  76   6 236]]
epoch: 51, train_loss: 9.0911, train_acc: 81.9277, train_fscore: 81.7935, test_loss: 12.4828, test_acc: 67.5909, test_fscore: 68.368, time: 15.98 sec
epoch: 52, train_loss: 9.246, train_acc: 81.9966, train_fscore: 81.9112, test_loss: 12.6978, test_acc: 68.9464, test_fscore: 69.33, time: 16.01 sec
epoch: 53, train_loss: 8.9793, train_acc: 81.6695, train_fscore: 81.5481, test_loss: 10.4956, test_acc: 68.6999, test_fscore: 69.0911, time: 15.99 sec
epoch: 54, train_loss: 8.4061, train_acc: 82.2203, train_fscore: 82.0633, test_loss: 10.8483, test_acc: 69.3777, test_fscore: 69.7357, time: 16.0 sec
epoch: 55, train_loss: 8.061, train_acc: 83.0293, train_fscore: 82.9255, test_loss: 11.3725, test_acc: 67.2212, test_fscore: 67.8923, time: 16.0 sec
epoch: 56, train_loss: 8.1955, train_acc: 83.2874, train_fscore: 83.1833, test_loss: 11.0599, test_acc: 70.5484, test_fscore: 70.9243, time: 16.0 sec
epoch: 57, train_loss: 8.1413, train_acc: 83.2874, train_fscore: 83.1579, test_loss: 9.9544, test_acc: 68.6999, test_fscore: 69.0663, time: 15.97 sec
epoch: 58, train_loss: 7.6069, train_acc: 84.0103, train_fscore: 83.9445, test_loss: 10.0032, test_acc: 67.8373, test_fscore: 68.0561, time: 15.82 sec
epoch: 59, train_loss: 7.6024, train_acc: 83.0637, train_fscore: 82.9543, test_loss: 9.7611, test_acc: 70.1171, test_fscore: 70.6382, time: 15.99 sec
epoch: 60, train_loss: 7.478, train_acc: 83.1842, train_fscore: 83.0673, test_loss: 9.608, test_acc: 70.4251, test_fscore: 70.6449, time: 15.98 sec
----------best F-Score: 70.9762
              precision    recall  f1-score   support

           0     0.5204    0.7083    0.6000       144
           1     0.8475    0.7714    0.8077       245
           2     0.7094    0.7057    0.7076       384
           3     0.5864    0.7588    0.6615       170
           4     0.8185    0.7692    0.7931       299
           5     0.6916    0.5827    0.6325       381

    accuracy                         0.7043      1623
   macro avg     0.6956    0.7160    0.7004      1623
weighted avg     0.7165    0.7043    0.7064      1623

[[102   2   3   0  36   1]
 [  4 189  22   3   1  26]
 [ 43  18 271   6   8  38]
 [  0   2   7 129   0  32]
 [ 43   1  23   0 230   2]
 [  4  11  56  82   6 222]]
epoch: 61, train_loss: 7.3215, train_acc: 83.58, train_fscore: 83.4888, test_loss: 9.6839, test_acc: 68.5767, test_fscore: 69.1297, time: 15.99 sec

*** 新的最优F1分数: 72.2250% (Epoch 62) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 74.3056% (144 样本)
sad: 75.1020% (245 样本)
neutral: 73.9583% (384 样本)
angry: 72.9412% (170 样本)
excited: 74.2475% (299 样本)
frustrated: 64.8294% (381 样本)

每个类别的F1分数:
happy: 62.3907%
sad: 79.3103%
neutral: 71.9899%
angry: 69.8592%
excited: 78.7234%
frustrated: 67.5787%

总体指标:
总体准确率: 71.9655%
加权准确率 (W_ACC, 按测试集分布): 71.9655%
总体F1分数 (不加权宏平均): 71.6420%
手动计算宏平均F1: 71.6420%
加权F1 (W_F1, 按测试集分布): 72.2250%
============================================================
epoch: 62, train_loss: 7.3158, train_acc: 83.9243, train_fscore: 83.8129, test_loss: 9.6068, test_acc: 71.9655, test_fscore: 72.225, time: 16.0 sec
epoch: 63, train_loss: 7.2087, train_acc: 84.3718, train_fscore: 84.2655, test_loss: 9.2541, test_acc: 69.008, test_fscore: 69.5377, time: 16.0 sec
epoch: 64, train_loss: 7.1773, train_acc: 84.3201, train_fscore: 84.2185, test_loss: 9.2452, test_acc: 70.7332, test_fscore: 70.9084, time: 15.99 sec
epoch: 65, train_loss: 7.0237, train_acc: 84.148, train_fscore: 84.028, test_loss: 9.2096, test_acc: 69.8706, test_fscore: 70.2482, time: 16.0 sec
epoch: 66, train_loss: 7.0194, train_acc: 84.148, train_fscore: 84.0599, test_loss: 9.0397, test_acc: 71.2261, test_fscore: 71.5916, time: 15.99 sec
epoch: 67, train_loss: 6.926, train_acc: 83.9415, train_fscore: 83.808, test_loss: 8.9378, test_acc: 69.5009, test_fscore: 69.8324, time: 15.99 sec
epoch: 68, train_loss: 6.9266, train_acc: 85.0258, train_fscore: 84.9635, test_loss: 8.984, test_acc: 69.1929, test_fscore: 69.5341, time: 16.02 sec
epoch: 69, train_loss: 6.8773, train_acc: 84.5267, train_fscore: 84.4157, test_loss: 8.7537, test_acc: 71.7807, test_fscore: 72.0722, time: 15.98 sec
epoch: 70, train_loss: 6.7066, train_acc: 84.6299, train_fscore: 84.5072, test_loss: 8.7646, test_acc: 71.2877, test_fscore: 71.5467, time: 15.98 sec
----------best F-Score: 72.225
              precision    recall  f1-score   support

           0     0.5350    0.7431    0.6221       144
           1     0.8611    0.7592    0.8069       245
           2     0.7113    0.7188    0.7150       384
           3     0.6458    0.7294    0.6851       170
           4     0.8266    0.7492    0.7860       299
           5     0.6966    0.6509    0.6730       381

    accuracy                         0.7178      1623
   macro avg     0.7127    0.7251    0.7147      1623
weighted avg     0.7292    0.7178    0.7207      1623

[[107   1   1   0  34   1]
 [  5 186  27   3   1  23]
 [ 38  15 276   6   6  43]
 [  0   2   5 124   0  39]
 [ 47   0  26   0 224   2]
 [  3  12  53  59   6 248]]
epoch: 71, train_loss: 6.679, train_acc: 84.6988, train_fscore: 84.5829, test_loss: 8.6887, test_acc: 70.3635, test_fscore: 70.8028, time: 16.01 sec
epoch: 72, train_loss: 6.7526, train_acc: 85.0947, train_fscore: 85.0092, test_loss: 8.8836, test_acc: 70.4868, test_fscore: 70.752, time: 15.98 sec
epoch: 73, train_loss: 6.6613, train_acc: 85.4217, train_fscore: 85.3127, test_loss: 8.6283, test_acc: 71.1645, test_fscore: 71.4987, time: 15.99 sec
epoch: 74, train_loss: 6.6878, train_acc: 85.3356, train_fscore: 85.2094, test_loss: 8.7152, test_acc: 69.6242, test_fscore: 69.8684, time: 16.02 sec
epoch: 75, train_loss: 6.709, train_acc: 85.4045, train_fscore: 85.3136, test_loss: 9.0132, test_acc: 71.2261, test_fscore: 71.5406, time: 15.98 sec
epoch: 76, train_loss: 6.7208, train_acc: 85.6627, train_fscore: 85.4876, test_loss: 8.7774, test_acc: 71.719, test_fscore: 72.0122, time: 16.0 sec
epoch: 77, train_loss: 6.7383, train_acc: 85.5938, train_fscore: 85.52, test_loss: 8.8905, test_acc: 70.1171, test_fscore: 70.2517, time: 16.0 sec
epoch: 78, train_loss: 6.5653, train_acc: 85.6971, train_fscore: 85.5813, test_loss: 8.7489, test_acc: 71.411, test_fscore: 71.7606, time: 16.02 sec
epoch: 79, train_loss: 6.6128, train_acc: 86.8158, train_fscore: 86.7078, test_loss: 8.6803, test_acc: 71.2877, test_fscore: 71.58, time: 16.37 sec
epoch: 80, train_loss: 6.5171, train_acc: 86.3339, train_fscore: 86.232, test_loss: 8.8606, test_acc: 70.4868, test_fscore: 70.8152, time: 15.99 sec
----------best F-Score: 72.225
              precision    recall  f1-score   support

           0     0.5266    0.7569    0.6211       144
           1     0.8683    0.7265    0.7911       245
           2     0.7408    0.6849    0.7118       384
           3     0.5962    0.7471    0.6632       170
           4     0.8287    0.7926    0.8103       299
           5     0.6751    0.6325    0.6531       381

    accuracy                         0.7116      1623
   macro avg     0.7059    0.7234    0.7084      1623
weighted avg     0.7267    0.7116    0.7150      1623

[[109   1   0   0  33   1]
 [  7 178  27   3   1  29]
 [ 43  15 263   7   8  48]
 [  0   2   4 127   0  37]
 [ 44   0  17   0 237   1]
 [  4   9  44  76   7 241]]
epoch: 81, train_loss: 6.5913, train_acc: 86.3339, train_fscore: 86.2207, test_loss: 8.7277, test_acc: 71.1645, test_fscore: 71.4585, time: 15.98 sec

*** 新的最优F1分数: 72.2702% (Epoch 82) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.7500% (144 样本)
sad: 74.6939% (245 样本)
neutral: 72.9167% (384 样本)
angry: 73.5294% (170 样本)
excited: 77.2575% (299 样本)
frustrated: 66.1417% (381 样本)

每个类别的F1分数:
happy: 62.6582%
sad: 79.5652%
neutral: 71.7029%
angry: 69.0608%
excited: 79.2453%
frustrated: 67.7419%

总体指标:
总体准确率: 72.0887%
加权准确率 (W_ACC, 按测试集分布): 72.0887%
总体F1分数 (不加权宏平均): 71.6624%
手动计算宏平均F1: 71.6624%
加权F1 (W_F1, 按测试集分布): 72.2702%
============================================================
epoch: 82, train_loss: 6.5852, train_acc: 86.0241, train_fscore: 85.9218, test_loss: 8.6273, test_acc: 72.0887, test_fscore: 72.2702, time: 16.01 sec
epoch: 83, train_loss: 6.5278, train_acc: 86.5404, train_fscore: 86.4435, test_loss: 8.6078, test_acc: 70.5484, test_fscore: 70.8668, time: 15.99 sec
epoch: 84, train_loss: 6.5485, train_acc: 87.2633, train_fscore: 87.1571, test_loss: 8.5107, test_acc: 70.3019, test_fscore: 70.5493, time: 16.01 sec
epoch: 85, train_loss: 6.4876, train_acc: 87.0052, train_fscore: 86.8969, test_loss: 8.5744, test_acc: 70.7332, test_fscore: 71.1187, time: 16.0 sec
epoch: 86, train_loss: 6.516, train_acc: 86.8503, train_fscore: 86.7199, test_loss: 8.6555, test_acc: 71.8423, test_fscore: 72.0292, time: 15.98 sec
epoch: 87, train_loss: 6.4405, train_acc: 86.7642, train_fscore: 86.6608, test_loss: 8.5106, test_acc: 71.719, test_fscore: 71.8063, time: 15.95 sec
epoch: 88, train_loss: 6.503, train_acc: 87.1601, train_fscore: 87.0637, test_loss: 8.5599, test_acc: 70.3635, test_fscore: 70.7563, time: 15.84 sec
epoch: 89, train_loss: 6.4329, train_acc: 87.5559, train_fscore: 87.451, test_loss: 8.5673, test_acc: 71.2877, test_fscore: 71.6827, time: 15.99 sec
epoch: 90, train_loss: 6.4084, train_acc: 86.988, train_fscore: 86.8765, test_loss: 8.5023, test_acc: 71.2877, test_fscore: 71.556, time: 15.99 sec
----------best F-Score: 72.2702
              precision    recall  f1-score   support

           0     0.5556    0.7292    0.6306       144
           1     0.8486    0.7551    0.7991       245
           2     0.7231    0.7005    0.7116       384
           3     0.6028    0.7588    0.6719       170
           4     0.8346    0.7592    0.7951       299
           5     0.6760    0.6352    0.6549       381

    accuracy                         0.7129      1623
   macro avg     0.7068    0.7230    0.7106      1623
weighted avg     0.7241    0.7129    0.7156      1623

[[105   1   5   0  32   1]
 [  4 185  22   3   1  30]
 [ 33  20 269   6   6  50]
 [  0   3   5 129   0  33]
 [ 45   2  23   0 227   2]
 [  2   7  48  76   6 242]]
epoch: 91, train_loss: 6.4189, train_acc: 87.9518, train_fscore: 87.8622, test_loss: 8.5485, test_acc: 71.2261, test_fscore: 71.3108, time: 15.99 sec
epoch: 92, train_loss: 6.3287, train_acc: 88.1756, train_fscore: 88.0643, test_loss: 8.5895, test_acc: 72.0887, test_fscore: 72.256, time: 15.99 sec
epoch: 93, train_loss: 6.3563, train_acc: 87.883, train_fscore: 87.7601, test_loss: 8.5707, test_acc: 70.1787, test_fscore: 70.4536, time: 16.0 sec
epoch: 94, train_loss: 6.3136, train_acc: 87.401, train_fscore: 87.3297, test_loss: 8.5071, test_acc: 71.2261, test_fscore: 71.1662, time: 16.0 sec
epoch: 95, train_loss: 6.3098, train_acc: 87.7453, train_fscore: 87.6331, test_loss: 8.5073, test_acc: 71.1645, test_fscore: 71.3759, time: 15.99 sec

*** 新的最优F1分数: 72.3830% (Epoch 96) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.0556% (144 样本)
sad: 71.4286% (245 样本)
neutral: 76.3021% (384 样本)
angry: 72.3529% (170 样本)
excited: 77.2575% (299 样本)
frustrated: 65.8793% (381 样本)

每个类别的F1分数:
happy: 61.0592%
sad: 79.0068%
neutral: 71.9018%
angry: 69.4915%
excited: 79.5181%
frustrated: 68.5792%

总体指标:
总体准确率: 72.1503%
加权准确率 (W_ACC, 按测试集分布): 72.1503%
总体F1分数 (不加权宏平均): 71.5928%
手动计算宏平均F1: 71.5928%
加权F1 (W_F1, 按测试集分布): 72.3830%
============================================================
epoch: 96, train_loss: 6.319, train_acc: 87.6592, train_fscore: 87.5561, test_loss: 8.3174, test_acc: 72.1503, test_fscore: 72.383, time: 15.99 sec
epoch: 97, train_loss: 6.3144, train_acc: 87.7625, train_fscore: 87.6794, test_loss: 8.7516, test_acc: 71.411, test_fscore: 71.602, time: 16.19 sec
epoch: 98, train_loss: 6.349, train_acc: 88.1411, train_fscore: 88.0242, test_loss: 8.4988, test_acc: 71.1645, test_fscore: 71.4597, time: 16.0 sec
epoch: 99, train_loss: 6.2991, train_acc: 88.6231, train_fscore: 88.5286, test_loss: 8.6841, test_acc: 71.1645, test_fscore: 71.3239, time: 15.99 sec
epoch: 100, train_loss: 6.2866, train_acc: 88.7608, train_fscore: 88.6687, test_loss: 8.4011, test_acc: 72.0271, test_fscore: 72.1312, time: 15.8 sec
----------best F-Score: 72.383
              precision    recall  f1-score   support

           0     0.5537    0.6806    0.6106       144
           1     0.8838    0.7143    0.7901       245
           2     0.6798    0.7630    0.7190       384
           3     0.6685    0.7235    0.6949       170
           4     0.8191    0.7726    0.7952       299
           5     0.7151    0.6588    0.6858       381

    accuracy                         0.7215      1623
   macro avg     0.7200    0.7188    0.7159      1623
weighted avg     0.7322    0.7215    0.7238      1623

[[ 98   1   6   0  38   1]
 [  6 175  34   2   1  27]
 [ 35  12 293   4   7  33]
 [  0   3   6 123   0  38]
 [ 36   1  30   0 231   1]
 [  2   6  62  55   5 251]]
epoch: 101, train_loss: 6.2271, train_acc: 89.4148, train_fscore: 89.3473, test_loss: 8.6643, test_acc: 71.2877, test_fscore: 71.3342, time: 16.2 sec
epoch: 102, train_loss: 6.356, train_acc: 88.7091, train_fscore: 88.6017, test_loss: 8.6928, test_acc: 70.7948, test_fscore: 70.8619, time: 15.98 sec
epoch: 103, train_loss: 6.2396, train_acc: 89.1394, train_fscore: 89.0707, test_loss: 8.9572, test_acc: 71.8423, test_fscore: 72.0202, time: 16.0 sec
epoch: 104, train_loss: 6.2091, train_acc: 89.2083, train_fscore: 89.1281, test_loss: 8.7864, test_acc: 71.2261, test_fscore: 71.5196, time: 16.19 sec
epoch: 105, train_loss: 6.307, train_acc: 88.7435, train_fscore: 88.6513, test_loss: 8.7136, test_acc: 71.3494, test_fscore: 71.1919, time: 15.8 sec
epoch: 106, train_loss: 6.2186, train_acc: 89.5353, train_fscore: 89.4716, test_loss: 8.6017, test_acc: 69.9322, test_fscore: 70.3515, time: 16.0 sec
epoch: 107, train_loss: 6.1846, train_acc: 89.6041, train_fscore: 89.5159, test_loss: 8.9069, test_acc: 71.2877, test_fscore: 71.2305, time: 15.98 sec
epoch: 108, train_loss: 6.2306, train_acc: 89.4492, train_fscore: 89.3672, test_loss: 8.8266, test_acc: 71.9655, test_fscore: 72.1609, time: 16.0 sec
epoch: 109, train_loss: 6.2005, train_acc: 89.5009, train_fscore: 89.4048, test_loss: 8.6949, test_acc: 72.0887, test_fscore: 72.2397, time: 16.0 sec
epoch: 110, train_loss: 6.1614, train_acc: 89.8795, train_fscore: 89.791, test_loss: 8.6438, test_acc: 71.411, test_fscore: 71.6992, time: 15.99 sec
----------best F-Score: 72.383
              precision    recall  f1-score   support

           0     0.5537    0.6806    0.6106       144
           1     0.8838    0.7143    0.7901       245
           2     0.6798    0.7630    0.7190       384
           3     0.6685    0.7235    0.6949       170
           4     0.8191    0.7726    0.7952       299
           5     0.7151    0.6588    0.6858       381

    accuracy                         0.7215      1623
   macro avg     0.7200    0.7188    0.7159      1623
weighted avg     0.7322    0.7215    0.7238      1623

[[ 98   1   6   0  38   1]
 [  6 175  34   2   1  27]
 [ 35  12 293   4   7  33]
 [  0   3   6 123   0  38]
 [ 36   1  30   0 231   1]
 [  2   6  62  55   5 251]]
epoch: 111, train_loss: 6.1298, train_acc: 90.0344, train_fscore: 89.9436, test_loss: 8.7964, test_acc: 71.411, test_fscore: 71.5123, time: 15.99 sec
epoch: 112, train_loss: 6.2287, train_acc: 90.2065, train_fscore: 90.1307, test_loss: 8.7097, test_acc: 71.9039, test_fscore: 72.0286, time: 16.0 sec
epoch: 113, train_loss: 6.2452, train_acc: 90.0, train_fscore: 89.8993, test_loss: 8.6437, test_acc: 71.8423, test_fscore: 72.04, time: 15.99 sec
epoch: 114, train_loss: 6.1589, train_acc: 90.1205, train_fscore: 90.042, test_loss: 8.7041, test_acc: 71.4726, test_fscore: 71.7167, time: 16.01 sec
epoch: 115, train_loss: 6.0378, train_acc: 90.9294, train_fscore: 90.8682, test_loss: 8.7907, test_acc: 71.2261, test_fscore: 71.3319, time: 16.0 sec
epoch: 116, train_loss: 6.1181, train_acc: 90.9122, train_fscore: 90.8392, test_loss: 8.5866, test_acc: 71.5342, test_fscore: 71.7749, time: 16.17 sec
epoch: 117, train_loss: 6.1728, train_acc: 90.4131, train_fscore: 90.3237, test_loss: 8.515, test_acc: 72.0887, test_fscore: 72.1476, time: 16.02 sec

*** 新的最优F1分数: 72.8610% (Epoch 118) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.1389% (144 样本)
sad: 72.6531% (245 样本)
neutral: 78.1250% (384 样本)
angry: 67.6471% (170 样本)
excited: 74.9164% (299 样本)
frustrated: 68.5039% (381 样本)

每个类别的F1分数:
happy: 62.9283%
sad: 79.2873%
neutral: 73.1707%
angry: 68.0473%
excited: 78.8732%
frustrated: 69.6000%

总体指标:
总体准确率: 72.6433%
加权准确率 (W_ACC, 按测试集分布): 72.6433%
总体F1分数 (不加权宏平均): 71.9845%
手动计算宏平均F1: 71.9845%
加权F1 (W_F1, 按测试集分布): 72.8610%
============================================================
epoch: 118, train_loss: 6.0881, train_acc: 90.5164, train_fscore: 90.4143, test_loss: 8.7597, test_acc: 72.6433, test_fscore: 72.861, time: 15.95 sec
epoch: 119, train_loss: 6.1171, train_acc: 90.327, train_fscore: 90.241, test_loss: 8.696, test_acc: 71.6574, test_fscore: 71.8336, time: 15.83 sec
epoch: 120, train_loss: 6.1424, train_acc: 90.5508, train_fscore: 90.4782, test_loss: 8.7481, test_acc: 72.0271, test_fscore: 72.0298, time: 15.81 sec
----------best F-Score: 72.861
              precision    recall  f1-score   support

           0     0.5706    0.7014    0.6293       144
           1     0.8725    0.7265    0.7929       245
           2     0.6881    0.7812    0.7317       384
           3     0.6845    0.6765    0.6805       170
           4     0.8327    0.7492    0.7887       299
           5     0.7073    0.6850    0.6960       381

    accuracy                         0.7264      1623
   macro avg     0.7260    0.7200    0.7198      1623
weighted avg     0.7363    0.7264    0.7286      1623

[[101   1   6   0  35   1]
 [  3 178  33   2   1  28]
 [ 29  13 300   5   4  33]
 [  0   3   7 115   0  45]
 [ 42   2  30   0 224   1]
 [  2   7  60  46   5 261]]
epoch: 121, train_loss: 6.0916, train_acc: 91.0155, train_fscore: 90.9263, test_loss: 8.5849, test_acc: 70.9181, test_fscore: 71.1191, time: 15.97 sec
epoch: 122, train_loss: 6.0008, train_acc: 91.4974, train_fscore: 91.4134, test_loss: 8.7166, test_acc: 71.4726, test_fscore: 71.6569, time: 16.0 sec
epoch: 123, train_loss: 6.0519, train_acc: 91.4974, train_fscore: 91.4389, test_loss: 8.6632, test_acc: 71.719, test_fscore: 71.8059, time: 15.99 sec
epoch: 124, train_loss: 6.0735, train_acc: 91.2909, train_fscore: 91.2144, test_loss: 8.6856, test_acc: 72.2736, test_fscore: 72.3796, time: 16.0 sec
epoch: 125, train_loss: 6.0441, train_acc: 91.4802, train_fscore: 91.4148, test_loss: 9.0768, test_acc: 71.5958, test_fscore: 71.7117, time: 15.99 sec
epoch: 126, train_loss: 6.1301, train_acc: 91.7728, train_fscore: 91.7214, test_loss: 8.9499, test_acc: 72.7049, test_fscore: 72.8003, time: 16.0 sec
epoch: 127, train_loss: 6.1048, train_acc: 91.0843, train_fscore: 90.9987, test_loss: 9.0038, test_acc: 71.5342, test_fscore: 71.7723, time: 15.99 sec
epoch: 128, train_loss: 6.1623, train_acc: 91.7384, train_fscore: 91.6916, test_loss: 9.1263, test_acc: 71.4726, test_fscore: 71.5085, time: 15.99 sec
epoch: 129, train_loss: 6.0629, train_acc: 91.3941, train_fscore: 91.3285, test_loss: 9.132, test_acc: 71.2261, test_fscore: 71.209, time: 15.99 sec
epoch: 130, train_loss: 6.1201, train_acc: 91.8933, train_fscore: 91.8165, test_loss: 9.3418, test_acc: 72.212, test_fscore: 72.4453, time: 16.0 sec
----------best F-Score: 72.861
              precision    recall  f1-score   support

           0     0.5706    0.7014    0.6293       144
           1     0.8725    0.7265    0.7929       245
           2     0.6881    0.7812    0.7317       384
           3     0.6845    0.6765    0.6805       170
           4     0.8327    0.7492    0.7887       299
           5     0.7073    0.6850    0.6960       381

    accuracy                         0.7264      1623
   macro avg     0.7260    0.7200    0.7198      1623
weighted avg     0.7363    0.7264    0.7286      1623

[[101   1   6   0  35   1]
 [  3 178  33   2   1  28]
 [ 29  13 300   5   4  33]
 [  0   3   7 115   0  45]
 [ 42   2  30   0 224   1]
 [  2   7  60  46   5 261]]
epoch: 131, train_loss: 6.102, train_acc: 91.7728, train_fscore: 91.7115, test_loss: 9.1216, test_acc: 72.2736, test_fscore: 72.3937, time: 16.0 sec
epoch: 132, train_loss: 6.1391, train_acc: 91.7212, train_fscore: 91.673, test_loss: 9.1893, test_acc: 70.1787, test_fscore: 70.3549, time: 15.99 sec
epoch: 133, train_loss: 5.9705, train_acc: 91.8933, train_fscore: 91.8257, test_loss: 9.718, test_acc: 72.3352, test_fscore: 72.4873, time: 15.99 sec
epoch: 134, train_loss: 6.1604, train_acc: 92.3408, train_fscore: 92.2804, test_loss: 9.2422, test_acc: 71.5342, test_fscore: 71.5775, time: 16.01 sec
epoch: 135, train_loss: 6.2764, train_acc: 92.4441, train_fscore: 92.38, test_loss: 9.4986, test_acc: 71.1029, test_fscore: 71.1439, time: 15.98 sec
epoch: 136, train_loss: 6.2541, train_acc: 92.2375, train_fscore: 92.1664, test_loss: 9.1356, test_acc: 72.3352, test_fscore: 72.4973, time: 16.0 sec
epoch: 137, train_loss: 6.096, train_acc: 92.2892, train_fscore: 92.232, test_loss: 9.9187, test_acc: 71.9039, test_fscore: 71.7675, time: 15.99 sec
epoch: 138, train_loss: 6.3418, train_acc: 92.4441, train_fscore: 92.3845, test_loss: 9.5189, test_acc: 70.61, test_fscore: 70.7727, time: 15.99 sec
epoch: 139, train_loss: 6.2947, train_acc: 92.2719, train_fscore: 92.2169, test_loss: 9.799, test_acc: 71.411, test_fscore: 71.1863, time: 15.99 sec
epoch: 140, train_loss: 6.4195, train_acc: 92.3408, train_fscore: 92.2799, test_loss: 9.2381, test_acc: 71.8423, test_fscore: 71.8628, time: 15.83 sec
----------best F-Score: 72.861
              precision    recall  f1-score   support

           0     0.5706    0.7014    0.6293       144
           1     0.8725    0.7265    0.7929       245
           2     0.6881    0.7812    0.7317       384
           3     0.6845    0.6765    0.6805       170
           4     0.8327    0.7492    0.7887       299
           5     0.7073    0.6850    0.6960       381

    accuracy                         0.7264      1623
   macro avg     0.7260    0.7200    0.7198      1623
weighted avg     0.7363    0.7264    0.7286      1623

[[101   1   6   0  35   1]
 [  3 178  33   2   1  28]
 [ 29  13 300   5   4  33]
 [  0   3   7 115   0  45]
 [ 42   2  30   0 224   1]
 [  2   7  60  46   5 261]]
epoch: 141, train_loss: 6.1853, train_acc: 92.9088, train_fscore: 92.8507, test_loss: 10.4946, test_acc: 71.5958, test_fscore: 71.7245, time: 15.99 sec
epoch: 142, train_loss: 6.4131, train_acc: 92.1687, train_fscore: 92.107, test_loss: 9.5094, test_acc: 71.5958, test_fscore: 71.642, time: 15.98 sec
epoch: 143, train_loss: 6.2995, train_acc: 92.6334, train_fscore: 92.5802, test_loss: 9.9399, test_acc: 72.4584, test_fscore: 72.585, time: 15.99 sec
epoch: 144, train_loss: 6.311, train_acc: 92.9604, train_fscore: 92.9081, test_loss: 9.18, test_acc: 71.5958, test_fscore: 71.6969, time: 16.0 sec
epoch: 145, train_loss: 6.0478, train_acc: 92.6678, train_fscore: 92.6121, test_loss: 10.028, test_acc: 71.6574, test_fscore: 71.55, time: 16.0 sec
epoch: 146, train_loss: 6.3768, train_acc: 93.3563, train_fscore: 93.312, test_loss: 9.7349, test_acc: 70.7332, test_fscore: 70.534, time: 15.98 sec
epoch: 147, train_loss: 6.2212, train_acc: 93.0981, train_fscore: 93.0435, test_loss: 9.6536, test_acc: 72.0887, test_fscore: 72.2105, time: 16.01 sec
epoch: 148, train_loss: 6.0844, train_acc: 93.3219, train_fscore: 93.2797, test_loss: 10.202, test_acc: 71.4726, test_fscore: 71.5952, time: 15.96 sec
epoch: 149, train_loss: 6.3095, train_acc: 93.2186, train_fscore: 93.1672, test_loss: 9.3361, test_acc: 71.5958, test_fscore: 71.5694, time: 16.02 sec
epoch: 150, train_loss: 6.1511, train_acc: 93.3046, train_fscore: 93.2529, test_loss: 10.0544, test_acc: 71.1029, test_fscore: 71.1637, time: 15.99 sec
----------best F-Score: 72.861
              precision    recall  f1-score   support

           0     0.5706    0.7014    0.6293       144
           1     0.8725    0.7265    0.7929       245
           2     0.6881    0.7812    0.7317       384
           3     0.6845    0.6765    0.6805       170
           4     0.8327    0.7492    0.7887       299
           5     0.7073    0.6850    0.6960       381

    accuracy                         0.7264      1623
   macro avg     0.7260    0.7200    0.7198      1623
weighted avg     0.7363    0.7264    0.7286      1623

[[101   1   6   0  35   1]
 [  3 178  33   2   1  28]
 [ 29  13 300   5   4  33]
 [  0   3   7 115   0  45]
 [ 42   2  30   0 224   1]
 [  2   7  60  46   5 261]]
Test performance..
Best F-Score: 72.861
              precision    recall  f1-score   support

           0     0.5706    0.7014    0.6293       144
           1     0.8725    0.7265    0.7929       245
           2     0.6881    0.7812    0.7317       384
           3     0.6845    0.6765    0.6805       170
           4     0.8327    0.7492    0.7887       299
           5     0.7073    0.6850    0.6960       381

    accuracy                         0.7264      1623
   macro avg     0.7260    0.7200    0.7198      1623
weighted avg     0.7363    0.7264    0.7286      1623

[[101   1   6   0  35   1]
 [  3 178  33   2   1  28]
 [ 29  13 300   5   4  33]
 [  0   3   7 115   0  45]
 [ 42   2  30   0 224   1]
 [  2   7  60  46   5 261]]

==================================================
DETAILED EVALUATION WITH WEIGHTS
==================================================

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.1389% (144 样本)
sad: 72.6531% (245 样本)
neutral: 78.1250% (384 样本)
angry: 67.6471% (170 样本)
excited: 74.9164% (299 样本)
frustrated: 68.5039% (381 样本)

每个类别的F1分数:
happy: 62.9283%
sad: 79.2873%
neutral: 73.1707%
angry: 68.0473%
excited: 78.8732%
frustrated: 69.6000%

总体指标:
总体准确率: 72.6433%
加权准确率 (W_ACC, 按测试集分布): 72.6433%
总体F1分数 (不加权宏平均): 71.9845%
手动计算宏平均F1: 71.9845%
加权F1 (W_F1, 按测试集分布): 72.8610%
============================================================

进程已结束，退出代码为 0
