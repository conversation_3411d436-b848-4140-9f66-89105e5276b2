Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=3, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 3
total parameters: 79687704
training parameters: 79687704

*** 新的最优F1分数: 21.6204% (Epoch 1) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 8.3333% (144 样本)
sad: 60.0000% (245 样本)
neutral: 43.2292% (384 样本)
angry: 70.0000% (170 样本)
excited: 0.6689% (299 样本)
frustrated: 0.5249% (381 样本)

每个类别的F1分数:
happy: 12.1212%
sad: 65.6250%
neutral: 29.6429%
angry: 29.9371%
excited: 1.3289%
frustrated: 1.0417%

总体指标:
总体准确率: 27.6032%
加权准确率 (W_ACC, 按测试集分布): 27.6032%
总体F1分数 (不加权宏平均): 23.2828%
手动计算宏平均F1: 23.2828%
加权F1 (W_F1, 按测试集分布): 21.6204%
============================================================
epoch: 1, train_loss: 14.5204, train_acc: 21.6867, train_fscore: 18.3292, test_loss: 13.5501, test_acc: 27.6032, test_fscore: 21.6204, time: 21.2 sec

*** 新的最优F1分数: 37.5551% (Epoch 2) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 13.8889% (144 样本)
sad: 88.9796% (245 样本)
neutral: 0.2604% (384 样本)
angry: 77.0588% (170 样本)
excited: 81.9398% (299 样本)
frustrated: 30.9711% (381 样本)

每个类别的F1分数:
happy: 15.2091%
sad: 67.5969%
neutral: 0.5195%
angry: 43.6667%
excited: 68.4358%
frustrated: 37.0487%

总体指标:
总体准确率: 45.1633%
加权准确率 (W_ACC, 按测试集分布): 45.1633%
总体F1分数 (不加权宏平均): 38.7461%
手动计算宏平均F1: 38.7461%
加权F1 (W_F1, 按测试集分布): 37.5551%
============================================================
epoch: 2, train_loss: 12.587, train_acc: 34.8021, train_fscore: 27.25, test_loss: 12.3126, test_acc: 45.1633, test_fscore: 37.5551, time: 20.55 sec

*** 新的最优F1分数: 52.2554% (Epoch 3) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 21.5278% (144 样本)
sad: 75.5102% (245 样本)
neutral: 45.3125% (384 样本)
angry: 83.5294% (170 样本)
excited: 82.2742% (299 样本)
frustrated: 25.7218% (381 样本)

每个类别的F1分数:
happy: 22.1429%
sad: 73.8523%
neutral: 48.4006%
angry: 52.9851%
excited: 75.3446%
frustrated: 35.1885%

总体指标:
总体准确率: 53.9741%
加权准确率 (W_ACC, 按测试集分布): 53.9741%
总体F1分数 (不加权宏平均): 51.3190%
手动计算宏平均F1: 51.3190%
加权F1 (W_F1, 按测试集分布): 52.2554%
============================================================
epoch: 3, train_loss: 11.5881, train_acc: 47.5215, train_fscore: 39.5643, test_loss: 11.6117, test_acc: 53.9741, test_fscore: 52.2554, time: 20.39 sec

*** 新的最优F1分数: 57.9132% (Epoch 4) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 59.0278% (144 样本)
sad: 77.5510% (245 样本)
neutral: 54.6875% (384 样本)
angry: 77.6471% (170 样本)
excited: 53.1773% (299 样本)
frustrated: 39.3701% (381 样本)

每个类别的F1分数:
happy: 35.5649%
sad: 74.8031%
neutral: 57.3770%
angry: 62.4113%
excited: 63.4731%
frustrated: 49.6689%

总体指标:
总体准确率: 57.0548%
加权准确率 (W_ACC, 按测试集分布): 57.0548%
总体F1分数 (不加权宏平均): 57.2164%
手动计算宏平均F1: 57.2164%
加权F1 (W_F1, 按测试集分布): 57.9132%
============================================================
epoch: 4, train_loss: 11.1245, train_acc: 58.8812, train_fscore: 57.058, test_loss: 11.3605, test_acc: 57.0548, test_fscore: 57.9132, time: 20.17 sec

*** 新的最优F1分数: 63.5533% (Epoch 5) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 27.0833% (144 样本)
sad: 78.3673% (245 样本)
neutral: 57.5521% (384 样本)
angry: 78.2353% (170 样本)
excited: 89.2977% (299 样本)
frustrated: 50.9186% (381 样本)

每个类别的F1分数:
happy: 29.1045%
sad: 75.7396%
neutral: 59.5687%
angry: 67.8571%
excited: 79.4643%
frustrated: 58.3459%

总体指标:
总体准确率: 64.4486%
加权准确率 (W_ACC, 按测试集分布): 64.4486%
总体F1分数 (不加权宏平均): 61.6800%
手动计算宏平均F1: 61.6800%
加权F1 (W_F1, 按测试集分布): 63.5533%
============================================================
epoch: 5, train_loss: 10.8338, train_acc: 62.9948, train_fscore: 62.2591, test_loss: 11.0772, test_acc: 64.4486, test_fscore: 63.5533, time: 20.06 sec
epoch: 6, train_loss: 10.596, train_acc: 64.5267, train_fscore: 63.0257, test_loss: 11.0378, test_acc: 60.6901, test_fscore: 61.3197, time: 20.18 sec

*** 新的最优F1分数: 63.8561% (Epoch 7) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 50.6944% (144 样本)
sad: 71.8367% (245 样本)
neutral: 72.9167% (384 样本)
angry: 71.7647% (170 样本)
excited: 71.5719% (299 样本)
frustrated: 44.6194% (381 样本)

每个类别的F1分数:
happy: 42.1965%
sad: 74.5763%
neutral: 64.5161%
angry: 67.0330%
excited: 74.5645%
frustrated: 54.6624%

总体指标:
总体准确率: 63.7708%
加权准确率 (W_ACC, 按测试集分布): 63.7708%
总体F1分数 (不加权宏平均): 62.9248%
手动计算宏平均F1: 62.9248%
加权F1 (W_F1, 按测试集分布): 63.8561%
============================================================
epoch: 7, train_loss: 10.4599, train_acc: 66.0069, train_fscore: 65.2978, test_loss: 10.793, test_acc: 63.7708, test_fscore: 63.8561, time: 20.25 sec
epoch: 8, train_loss: 10.3874, train_acc: 68.2616, train_fscore: 67.8573, test_loss: 10.8924, test_acc: 61.9224, test_fscore: 62.708, time: 19.99 sec

*** 新的最优F1分数: 66.4989% (Epoch 9) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 45.1389% (144 样本)
sad: 74.6939% (245 样本)
neutral: 67.9688% (384 样本)
angry: 69.4118% (170 样本)
excited: 77.5920% (299 样本)
frustrated: 57.4803% (381 样本)

每个类别的F1分数:
happy: 42.2078%
sad: 75.4639%
neutral: 65.3317%
angry: 66.8555%
excited: 76.6942%
frustrated: 62.9310%

总体指标:
总体准确率: 66.4202%
加权准确率 (W_ACC, 按测试集分布): 66.4202%
总体F1分数 (不加权宏平均): 64.9140%
手动计算宏平均F1: 64.9140%
加权F1 (W_F1, 按测试集分布): 66.4989%
============================================================
epoch: 9, train_loss: 10.2139, train_acc: 69.6041, train_fscore: 69.5247, test_loss: 10.6888, test_acc: 66.4202, test_fscore: 66.4989, time: 20.18 sec
epoch: 10, train_loss: 10.2059, train_acc: 70.3614, train_fscore: 69.9303, test_loss: 10.5677, test_acc: 65.1879, test_fscore: 65.8986, time: 20.23 sec
----------best F-Score: 66.4989
              precision    recall  f1-score   support

           0     0.3769    0.6806    0.4851       144
           1     0.7897    0.7510    0.7699       245
           2     0.6964    0.6094    0.6500       384
           3     0.5733    0.7588    0.6532       170
           4     0.8151    0.6488    0.7225       299
           5     0.6616    0.5748    0.6152       381

    accuracy                         0.6519      1623
   macro avg     0.6522    0.6706    0.6493      1623
weighted avg     0.6830    0.6519    0.6590      1623

[[ 98   3   6   1  33   3]
 [ 11 184  22   2   0  26]
 [ 56  26 234  18   5  45]
 [  0   0   4 129   0  37]
 [ 90   0  14   0 194   1]
 [  5  20  56  75   6 219]]
epoch: 11, train_loss: 10.1232, train_acc: 71.79, train_fscore: 71.7639, test_loss: 10.6242, test_acc: 65.3728, test_fscore: 65.353, time: 20.09 sec

*** 新的最优F1分数: 66.8049% (Epoch 12) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.0556% (144 样本)
sad: 64.8980% (245 样本)
neutral: 65.1042% (384 样本)
angry: 73.5294% (170 样本)
excited: 67.2241% (299 样本)
frustrated: 62.4672% (381 样本)

每个类别的F1分数:
happy: 49.7462%
sad: 74.2991%
neutral: 65.6168%
angry: 65.7895%
excited: 74.0331%
frustrated: 64.4114%

总体指标:
总体准确率: 65.9889%
加权准确率 (W_ACC, 按测试集分布): 65.9889%
总体F1分数 (不加权宏平均): 65.6493%
手动计算宏平均F1: 65.6493%
加权F1 (W_F1, 按测试集分布): 66.8049%
============================================================
epoch: 12, train_loss: 9.9754, train_acc: 71.3597, train_fscore: 70.9171, test_loss: 10.4514, test_acc: 65.9889, test_fscore: 66.8049, time: 20.19 sec
epoch: 13, train_loss: 9.9366, train_acc: 72.6678, train_fscore: 72.5023, test_loss: 10.4503, test_acc: 65.9889, test_fscore: 66.6153, time: 20.28 sec
epoch: 14, train_loss: 9.6902, train_acc: 73.1325, train_fscore: 72.8489, test_loss: 10.3183, test_acc: 65.3112, test_fscore: 65.7646, time: 20.13 sec

*** 新的最优F1分数: 67.2468% (Epoch 15) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 57.6389% (144 样本)
sad: 76.7347% (245 样本)
neutral: 55.4688% (384 样本)
angry: 79.4118% (170 样本)
excited: 78.2609% (299 样本)
frustrated: 61.9423% (381 样本)

每个类别的F1分数:
happy: 49.1124%
sad: 78.1705%
neutral: 62.5551%
angry: 66.8317%
excited: 77.4834%
frustrated: 63.9566%

总体指标:
总体准确率: 67.0980%
加权准确率 (W_ACC, 按测试集分布): 67.0980%
总体F1分数 (不加权宏平均): 66.3516%
手动计算宏平均F1: 66.3516%
加权F1 (W_F1, 按测试集分布): 67.2468%
============================================================
epoch: 15, train_loss: 9.5366, train_acc: 73.4596, train_fscore: 73.2468, test_loss: 10.1154, test_acc: 67.098, test_fscore: 67.2468, time: 20.45 sec
epoch: 16, train_loss: 9.5144, train_acc: 74.3546, train_fscore: 74.1406, test_loss: 10.2415, test_acc: 65.0647, test_fscore: 65.545, time: 20.12 sec
epoch: 17, train_loss: 9.4395, train_acc: 75.3873, train_fscore: 75.191, test_loss: 10.1342, test_acc: 64.1405, test_fscore: 64.5723, time: 20.22 sec

*** 新的最优F1分数: 68.7359% (Epoch 18) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.8333% (144 样本)
sad: 71.8367% (245 样本)
neutral: 68.2292% (384 样本)
angry: 63.5294% (170 样本)
excited: 65.5518% (299 样本)
frustrated: 68.5039% (381 样本)

每个类别的F1分数:
happy: 52.7132%
sad: 76.0259%
neutral: 67.2657%
angry: 68.1388%
excited: 73.6842%
frustrated: 67.9688%

总体指标:
总体准确率: 68.0838%
加权准确率 (W_ACC, 按测试集分布): 68.0838%
总体F1分数 (不加权宏平均): 67.6328%
手动计算宏平均F1: 67.6328%
加权F1 (W_F1, 按测试集分布): 68.7359%
============================================================
epoch: 18, train_loss: 9.3682, train_acc: 73.9587, train_fscore: 73.3846, test_loss: 9.9476, test_acc: 68.0838, test_fscore: 68.7359, time: 20.19 sec
epoch: 19, train_loss: 9.233, train_acc: 76.0585, train_fscore: 75.9563, test_loss: 9.9335, test_acc: 64.695, test_fscore: 64.7452, time: 20.25 sec

*** 新的最优F1分数: 69.1166% (Epoch 20) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 72.2222% (144 样本)
sad: 77.1429% (245 样本)
neutral: 60.9375% (384 样本)
angry: 65.8824% (170 样本)
excited: 67.5585% (299 样本)
frustrated: 71.6535% (381 样本)

每个类别的F1分数:
happy: 54.8813%
sad: 77.6181%
neutral: 65.6381%
angry: 67.4699%
excited: 75.5140%
frustrated: 68.2500%

总体指标:
总体准确率: 68.6383%
加权准确率 (W_ACC, 按测试集分布): 68.6383%
总体F1分数 (不加权宏平均): 68.2286%
手动计算宏平均F1: 68.2286%
加权F1 (W_F1, 按测试集分布): 69.1166%
============================================================
epoch: 20, train_loss: 9.1065, train_acc: 75.5077, train_fscore: 75.1441, test_loss: 9.8967, test_acc: 68.6383, test_fscore: 69.1166, time: 20.13 sec
----------best F-Score: 69.1166
              precision    recall  f1-score   support

           0     0.4426    0.7222    0.5488       144
           1     0.7810    0.7714    0.7762       245
           2     0.7112    0.6094    0.6564       384
           3     0.6914    0.6588    0.6747       170
           4     0.8559    0.6756    0.7551       299
           5     0.6516    0.7165    0.6825       381

    accuracy                         0.6864      1623
   macro avg     0.6889    0.6923    0.6823      1623
weighted avg     0.7085    0.6864    0.6912      1623

[[104   5   4   1  25   5]
 [  3 189  24   1   0  28]
 [ 49  29 234  11   3  58]
 [  0   0   4 112   0  54]
 [ 77   0  19   0 202   1]
 [  2  19  44  37   6 273]]
epoch: 21, train_loss: 9.0225, train_acc: 76.6954, train_fscore: 76.4752, test_loss: 9.8579, test_acc: 67.9606, test_fscore: 68.4859, time: 20.25 sec
epoch: 22, train_loss: 8.9074, train_acc: 78.4509, train_fscore: 78.3187, test_loss: 9.7773, test_acc: 68.0838, test_fscore: 68.7817, time: 20.14 sec
epoch: 23, train_loss: 8.8229, train_acc: 78.6403, train_fscore: 78.5213, test_loss: 9.7446, test_acc: 67.7141, test_fscore: 68.2025, time: 21.74 sec

*** 新的最优F1分数: 70.2980% (Epoch 24) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.1389% (144 样本)
sad: 81.2245% (245 样本)
neutral: 61.9792% (384 样本)
angry: 73.5294% (170 样本)
excited: 73.2441% (299 样本)
frustrated: 66.4042% (381 样本)

每个类别的F1分数:
happy: 56.9014%
sad: 81.2245%
neutral: 68.0000%
angry: 66.1376%
excited: 78.0749%
frustrated: 66.4042%

总体指标:
总体准确率: 69.9322%
加权准确率 (W_ACC, 按测试集分布): 69.9322%
总体F1分数 (不加权宏平均): 69.4571%
手动计算宏平均F1: 69.4571%
加权F1 (W_F1, 按测试集分布): 70.2980%
============================================================
epoch: 24, train_loss: 8.5773, train_acc: 78.778, train_fscore: 78.6336, test_loss: 9.2056, test_acc: 69.9322, test_fscore: 70.298, time: 22.63 sec
epoch: 25, train_loss: 8.3008, train_acc: 78.3821, train_fscore: 78.256, test_loss: 9.1327, test_acc: 68.6383, test_fscore: 69.2444, time: 20.15 sec
epoch: 26, train_loss: 8.0116, train_acc: 79.3115, train_fscore: 79.2109, test_loss: 8.7213, test_acc: 69.1929, test_fscore: 69.4794, time: 20.13 sec
epoch: 27, train_loss: 7.8026, train_acc: 79.6386, train_fscore: 79.5317, test_loss: 8.6155, test_acc: 68.207, test_fscore: 68.8954, time: 20.18 sec
epoch: 28, train_loss: 7.5796, train_acc: 80.4819, train_fscore: 80.3277, test_loss: 8.1991, test_acc: 68.3919, test_fscore: 68.8873, time: 20.19 sec
epoch: 29, train_loss: 7.3428, train_acc: 80.1721, train_fscore: 80.116, test_loss: 7.6916, test_acc: 69.4393, test_fscore: 69.4794, time: 20.2 sec
epoch: 30, train_loss: 7.1099, train_acc: 80.4131, train_fscore: 80.2753, test_loss: 7.4409, test_acc: 67.8373, test_fscore: 68.5024, time: 21.63 sec
----------best F-Score: 70.298
              precision    recall  f1-score   support

           0     0.4307    0.7986    0.5596       144
           1     0.8544    0.7184    0.7805       245
           2     0.6801    0.6589    0.6693       384
           3     0.6231    0.7294    0.6721       170
           4     0.8405    0.6522    0.7345       299
           5     0.6859    0.6247    0.6538       381

    accuracy                         0.6784      1623
   macro avg     0.6858    0.6970    0.6783      1623
weighted avg     0.7092    0.6784    0.6850      1623

[[115   1   6   0  21   1]
 [  9 176  31   3   0  26]
 [ 53  17 253  10   9  42]
 [  0   0   7 124   0  39]
 [ 82   0  21   0 195   1]
 [  8  12  54  62   7 238]]

*** 新的最优F1分数: 71.1100% (Epoch 31) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 72.9167% (144 样本)
sad: 80.4082% (245 样本)
neutral: 65.1042% (384 样本)
angry: 74.1176% (170 样本)
excited: 69.8997% (299 样本)
frustrated: 68.2415% (381 样本)

每个类别的F1分数:
happy: 57.2207%
sad: 81.7427%
neutral: 69.9301%
angry: 67.9245%
excited: 75.7246%
frustrated: 68.5112%

总体指标:
总体准确率: 70.6716%
加权准确率 (W_ACC, 按测试集分布): 70.6716%
总体F1分数 (不加权宏平均): 70.1756%
手动计算宏平均F1: 70.1756%
加权F1 (W_F1, 按测试集分布): 71.1100%
============================================================
epoch: 31, train_loss: 6.8003, train_acc: 79.9312, train_fscore: 79.6414, test_loss: 6.9738, test_acc: 70.6716, test_fscore: 71.11, time: 20.16 sec
epoch: 32, train_loss: 6.6198, train_acc: 81.1704, train_fscore: 81.1995, test_loss: 6.8349, test_acc: 68.2686, test_fscore: 68.1571, time: 19.98 sec
epoch: 33, train_loss: 6.3844, train_acc: 80.6368, train_fscore: 80.4802, test_loss: 6.6116, test_acc: 69.809, test_fscore: 70.0811, time: 20.24 sec
epoch: 34, train_loss: 6.1942, train_acc: 81.1704, train_fscore: 81.0167, test_loss: 6.5551, test_acc: 67.7141, test_fscore: 68.4264, time: 20.22 sec
epoch: 35, train_loss: 6.0425, train_acc: 81.9621, train_fscore: 81.869, test_loss: 6.4323, test_acc: 68.5151, test_fscore: 68.964, time: 20.44 sec
epoch: 36, train_loss: 5.9384, train_acc: 82.1687, train_fscore: 81.9819, test_loss: 6.3938, test_acc: 69.2545, test_fscore: 69.6698, time: 20.14 sec
epoch: 37, train_loss: 5.9551, train_acc: 81.2048, train_fscore: 81.1178, test_loss: 6.1584, test_acc: 70.4868, test_fscore: 70.1538, time: 20.32 sec
epoch: 38, train_loss: 5.8328, train_acc: 82.0826, train_fscore: 82.0409, test_loss: 6.0382, test_acc: 70.1787, test_fscore: 70.6469, time: 20.49 sec
epoch: 39, train_loss: 5.7262, train_acc: 82.1859, train_fscore: 82.0227, test_loss: 6.187, test_acc: 68.8232, test_fscore: 69.0039, time: 19.9 sec
epoch: 40, train_loss: 5.6614, train_acc: 82.7883, train_fscore: 82.7031, test_loss: 6.0615, test_acc: 68.6383, test_fscore: 68.957, time: 20.81 sec
----------best F-Score: 71.11
              precision    recall  f1-score   support

           0     0.4885    0.7361    0.5873       144
           1     0.8802    0.6898    0.7735       245
           2     0.7443    0.6823    0.7120       384
           3     0.6103    0.7647    0.6789       170
           4     0.8132    0.7425    0.7762       299
           5     0.6649    0.6562    0.6605       381

    accuracy                         0.7018      1623
   macro avg     0.7002    0.7119    0.6980      1623
weighted avg     0.7221    0.7018    0.7065      1623

[[106   1   6   0  30   1]
 [  8 169  26   3   1  38]
 [ 40  11 262  10  11  50]
 [  0   0   4 130   0  36]
 [ 60   0  16   0 222   1]
 [  3  11  38  70   9 250]]
epoch: 41, train_loss: 5.5695, train_acc: 82.5473, train_fscore: 82.3434, test_loss: 5.965, test_acc: 70.0555, test_fscore: 70.5935, time: 19.98 sec
epoch: 42, train_loss: 5.5774, train_acc: 82.9948, train_fscore: 82.9064, test_loss: 5.9126, test_acc: 68.1454, test_fscore: 67.9943, time: 19.78 sec

*** 新的最优F1分数: 71.8355% (Epoch 43) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.7500% (144 样本)
sad: 82.8571% (245 样本)
neutral: 66.4062% (384 样本)
angry: 76.4706% (170 样本)
excited: 78.2609% (299 样本)
frustrated: 63.5171% (381 样本)

每个类别的F1分数:
happy: 60.5505%
sad: 82.8571%
neutral: 71.7300%
angry: 66.6667%
excited: 79.0541%
frustrated: 65.7609%

总体指标:
总体准确率: 71.6574%
加权准确率 (W_ACC, 按测试集分布): 71.6574%
总体F1分数 (不加权宏平均): 71.1032%
手动计算宏平均F1: 71.1032%
加权F1 (W_F1, 按测试集分布): 71.8355%
============================================================
epoch: 43, train_loss: 5.5136, train_acc: 83.3907, train_fscore: 83.3467, test_loss: 5.7133, test_acc: 71.6574, test_fscore: 71.8355, time: 19.8 sec
epoch: 44, train_loss: 5.444, train_acc: 83.8898, train_fscore: 83.7436, test_loss: 5.8828, test_acc: 69.008, test_fscore: 69.3741, time: 20.06 sec
epoch: 45, train_loss: 5.3377, train_acc: 83.9243, train_fscore: 83.7971, test_loss: 5.7863, test_acc: 69.7474, test_fscore: 69.9391, time: 19.92 sec
epoch: 46, train_loss: 5.3043, train_acc: 84.148, train_fscore: 84.0248, test_loss: 5.8976, test_acc: 70.7948, test_fscore: 71.012, time: 19.99 sec
epoch: 47, train_loss: 5.2265, train_acc: 84.9398, train_fscore: 84.823, test_loss: 5.8599, test_acc: 70.0555, test_fscore: 70.3212, time: 19.85 sec
epoch: 48, train_loss: 5.1629, train_acc: 85.1979, train_fscore: 85.1071, test_loss: 5.8162, test_acc: 68.8848, test_fscore: 69.1815, time: 19.94 sec
epoch: 49, train_loss: 5.1535, train_acc: 84.6127, train_fscore: 84.5326, test_loss: 5.8537, test_acc: 70.7948, test_fscore: 70.9136, time: 20.01 sec
epoch: 50, train_loss: 5.111, train_acc: 85.1463, train_fscore: 85.015, test_loss: 5.8845, test_acc: 69.809, test_fscore: 69.9976, time: 20.02 sec
----------best F-Score: 71.8355
              precision    recall  f1-score   support

           0     0.5410    0.6875    0.6055       144
           1     0.8286    0.8286    0.8286       245
           2     0.7798    0.6641    0.7173       384
           3     0.5909    0.7647    0.6667       170
           4     0.7986    0.7826    0.7905       299
           5     0.6817    0.6352    0.6576       381

    accuracy                         0.7166      1623
   macro avg     0.7034    0.7271    0.7110      1623
weighted avg     0.7266    0.7166    0.7184      1623

[[ 99   1   5   0  38   1]
 [  3 203   8   3   0  28]
 [ 31  25 255  12  14  47]
 [  0   1   4 130   0  35]
 [ 49   0  14   0 234   2]
 [  1  15  41  75   7 242]]
epoch: 51, train_loss: 5.0531, train_acc: 85.611, train_fscore: 85.5134, test_loss: 5.6333, test_acc: 69.8706, test_fscore: 70.0778, time: 19.82 sec
epoch: 52, train_loss: 5.0948, train_acc: 86.2651, train_fscore: 86.1668, test_loss: 5.6573, test_acc: 70.2403, test_fscore: 70.4563, time: 19.96 sec
epoch: 53, train_loss: 5.0261, train_acc: 85.8692, train_fscore: 85.7653, test_loss: 5.725, test_acc: 70.0555, test_fscore: 70.206, time: 19.98 sec
epoch: 54, train_loss: 4.9657, train_acc: 86.8158, train_fscore: 86.7368, test_loss: 5.6334, test_acc: 70.0555, test_fscore: 70.2603, time: 19.76 sec
epoch: 55, train_loss: 4.9431, train_acc: 86.506, train_fscore: 86.3718, test_loss: 5.5981, test_acc: 71.2877, test_fscore: 71.4917, time: 19.81 sec
epoch: 56, train_loss: 4.9, train_acc: 86.4888, train_fscore: 86.3669, test_loss: 5.8293, test_acc: 68.9464, test_fscore: 69.03, time: 19.79 sec
epoch: 57, train_loss: 4.8781, train_acc: 87.1429, train_fscore: 87.046, test_loss: 5.6685, test_acc: 69.1312, test_fscore: 69.2359, time: 20.2 sec
epoch: 58, train_loss: 4.8763, train_acc: 86.7298, train_fscore: 86.6397, test_loss: 5.6774, test_acc: 70.1171, test_fscore: 70.3755, time: 19.99 sec
epoch: 59, train_loss: 4.7767, train_acc: 87.0224, train_fscore: 86.906, test_loss: 5.754, test_acc: 69.008, test_fscore: 69.4145, time: 19.83 sec
epoch: 60, train_loss: 4.7543, train_acc: 87.5215, train_fscore: 87.4399, test_loss: 5.6281, test_acc: 71.0413, test_fscore: 71.1479, time: 20.09 sec
----------best F-Score: 71.8355
              precision    recall  f1-score   support

           0     0.5417    0.7222    0.6190       144
           1     0.8348    0.7837    0.8084       245
           2     0.7282    0.7396    0.7339       384
           3     0.6087    0.7412    0.6684       170
           4     0.8083    0.7191    0.7611       299
           5     0.6982    0.6194    0.6565       381

    accuracy                         0.7129      1623
   macro avg     0.7033    0.7209    0.7079      1623
weighted avg     0.7229    0.7129    0.7149      1623

[[104   1   6   0  32   1]
 [  3 192  19   3   1  27]
 [ 25  22 284   7  11  35]
 [  0   1   6 126   0  37]
 [ 59   1  22   0 215   2]
 [  1  13  53  71   7 236]]
epoch: 61, train_loss: 4.8034, train_acc: 87.3838, train_fscore: 87.3111, test_loss: 5.6985, test_acc: 71.1645, test_fscore: 70.9086, time: 20.03 sec
epoch: 62, train_loss: 4.7818, train_acc: 86.9535, train_fscore: 86.8851, test_loss: 5.6477, test_acc: 70.4251, test_fscore: 70.5469, time: 19.86 sec
epoch: 63, train_loss: 4.678, train_acc: 87.6764, train_fscore: 87.5664, test_loss: 5.581, test_acc: 71.0413, test_fscore: 71.1711, time: 19.94 sec
epoch: 64, train_loss: 4.6348, train_acc: 88.1928, train_fscore: 88.1282, test_loss: 5.6041, test_acc: 71.0413, test_fscore: 71.1418, time: 20.0 sec
epoch: 65, train_loss: 4.6517, train_acc: 88.5026, train_fscore: 88.4182, test_loss: 5.7099, test_acc: 69.1929, test_fscore: 69.4951, time: 19.77 sec
epoch: 66, train_loss: 4.5778, train_acc: 88.6575, train_fscore: 88.59, test_loss: 5.5842, test_acc: 70.3635, test_fscore: 70.38, time: 20.0 sec
epoch: 67, train_loss: 4.6005, train_acc: 88.5026, train_fscore: 88.4135, test_loss: 5.6163, test_acc: 71.0413, test_fscore: 71.0072, time: 19.85 sec

*** 新的最优F1分数: 72.0194% (Epoch 68) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 67.3611% (144 样本)
sad: 74.6939% (245 样本)
neutral: 80.4688% (384 样本)
angry: 69.4118% (170 样本)
excited: 79.5987% (299 样本)
frustrated: 59.0551% (381 样本)

每个类别的F1分数:
happy: 65.1007%
sad: 78.8793%
neutral: 74.1897%
angry: 65.7382%
excited: 80.1347%
frustrated: 64.4699%

总体指标:
总体准确率: 72.0887%
加权准确率 (W_ACC, 按测试集分布): 72.0887%
总体F1分数 (不加权宏平均): 71.4187%
手动计算宏平均F1: 71.4187%
加权F1 (W_F1, 按测试集分布): 72.0194%
============================================================
epoch: 68, train_loss: 4.5935, train_acc: 88.4165, train_fscore: 88.346, test_loss: 5.5852, test_acc: 72.0887, test_fscore: 72.0194, time: 19.75 sec
epoch: 69, train_loss: 4.5609, train_acc: 88.0895, train_fscore: 88.0026, test_loss: 5.756, test_acc: 70.1787, test_fscore: 70.3976, time: 20.07 sec

*** 新的最优F1分数: 72.1053% (Epoch 70) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 82.6389% (144 样本)
sad: 84.4898% (245 样本)
neutral: 78.3854% (384 样本)
angry: 65.8824% (170 样本)
excited: 59.1973% (299 样本)
frustrated: 66.1417% (381 样本)

每个类别的F1分数:
happy: 61.9792%
sad: 83.1325%
neutral: 76.3959%
angry: 67.0659%
excited: 69.6850%
frustrated: 68.6649%

总体指标:
总体准确率: 71.9655%
加权准确率 (W_ACC, 按测试集分布): 71.9655%
总体F1分数 (不加权宏平均): 71.1539%
手动计算宏平均F1: 71.1539%
加权F1 (W_F1, 按测试集分布): 72.1053%
============================================================
epoch: 70, train_loss: 4.5014, train_acc: 89.6041, train_fscore: 89.5206, test_loss: 5.6006, test_acc: 71.9655, test_fscore: 72.1053, time: 20.12 sec
----------best F-Score: 72.1053
              precision    recall  f1-score   support

           0     0.4958    0.8264    0.6198       144
           1     0.8182    0.8449    0.8313       245
           2     0.7450    0.7839    0.7640       384
           3     0.6829    0.6588    0.6707       170
           4     0.8469    0.5920    0.6969       299
           5     0.7139    0.6614    0.6866       381

    accuracy                         0.7197      1623
   macro avg     0.7171    0.7279    0.7115      1623
weighted avg     0.7389    0.7197    0.7211      1623

[[119   1   5   0  18   1]
 [  3 207   8   2   1  24]
 [ 23  23 301   3   6  28]
 [  0   3   8 112   0  47]
 [ 94   1  26   0 177   1]
 [  1  18  56  47   7 252]]
epoch: 71, train_loss: 4.5235, train_acc: 88.537, train_fscore: 88.4579, test_loss: 5.6253, test_acc: 70.5484, test_fscore: 70.4999, time: 19.78 sec
epoch: 72, train_loss: 4.4341, train_acc: 89.8279, train_fscore: 89.7569, test_loss: 5.6448, test_acc: 69.5009, test_fscore: 69.472, time: 19.97 sec
epoch: 73, train_loss: 4.3914, train_acc: 89.191, train_fscore: 89.0993, test_loss: 5.5575, test_acc: 70.0555, test_fscore: 70.191, time: 19.88 sec
epoch: 74, train_loss: 4.454, train_acc: 89.5869, train_fscore: 89.5008, test_loss: 5.633, test_acc: 71.9039, test_fscore: 72.0547, time: 20.12 sec
epoch: 75, train_loss: 4.4092, train_acc: 89.9484, train_fscore: 89.8891, test_loss: 5.648, test_acc: 70.1171, test_fscore: 70.136, time: 22.31 sec
epoch: 76, train_loss: 4.4229, train_acc: 90.2754, train_fscore: 90.1918, test_loss: 5.603, test_acc: 70.9181, test_fscore: 71.0685, time: 20.09 sec
epoch: 77, train_loss: 4.4341, train_acc: 90.0688, train_fscore: 89.9842, test_loss: 5.7398, test_acc: 71.6574, test_fscore: 71.6922, time: 19.88 sec
epoch: 78, train_loss: 4.3543, train_acc: 89.8795, train_fscore: 89.8033, test_loss: 5.8555, test_acc: 69.0696, test_fscore: 68.9767, time: 19.9 sec
epoch: 79, train_loss: 4.3078, train_acc: 90.241, train_fscore: 90.1478, test_loss: 5.702, test_acc: 71.1029, test_fscore: 71.2277, time: 19.83 sec
epoch: 80, train_loss: 4.2508, train_acc: 90.7229, train_fscore: 90.6519, test_loss: 5.6785, test_acc: 70.61, test_fscore: 70.6993, time: 19.8 sec
----------best F-Score: 72.1053
              precision    recall  f1-score   support

           0     0.5417    0.7222    0.6190       144
           1     0.8440    0.7510    0.7948       245
           2     0.7085    0.7786    0.7419       384
           3     0.5625    0.7412    0.6396       170
           4     0.8147    0.7057    0.7563       299
           5     0.6916    0.5591    0.6183       381

    accuracy                         0.7006      1623
   macro avg     0.6938    0.7096    0.6950      1623
weighted avg     0.7145    0.7006    0.7019      1623

[[104   1   5   0  33   1]
 [  2 184  29   3   0  27]
 [ 25  16 299   4   9  31]
 [  0   3   7 126   0  34]
 [ 60   0  26   0 211   2]
 [  1  14  56  91   6 213]]
epoch: 81, train_loss: 4.2551, train_acc: 90.9294, train_fscore: 90.8498, test_loss: 5.5933, test_acc: 71.2877, test_fscore: 71.3911, time: 20.15 sec
epoch: 82, train_loss: 4.2358, train_acc: 90.6196, train_fscore: 90.549, test_loss: 5.7586, test_acc: 71.0413, test_fscore: 71.1528, time: 20.2 sec
epoch: 83, train_loss: 4.2647, train_acc: 90.7401, train_fscore: 90.6806, test_loss: 5.7201, test_acc: 70.9181, test_fscore: 70.8512, time: 19.96 sec
epoch: 84, train_loss: 4.2205, train_acc: 91.3081, train_fscore: 91.2547, test_loss: 5.6638, test_acc: 69.2545, test_fscore: 69.1676, time: 20.0 sec
epoch: 85, train_loss: 4.2261, train_acc: 91.4286, train_fscore: 91.3706, test_loss: 5.6748, test_acc: 71.8423, test_fscore: 71.8976, time: 19.97 sec
epoch: 86, train_loss: 4.1598, train_acc: 91.8417, train_fscore: 91.7859, test_loss: 5.7957, test_acc: 71.9039, test_fscore: 71.9729, time: 20.02 sec
epoch: 87, train_loss: 4.1376, train_acc: 91.3597, train_fscore: 91.2914, test_loss: 5.841, test_acc: 70.7948, test_fscore: 70.8488, time: 19.97 sec
epoch: 88, train_loss: 4.1777, train_acc: 91.5835, train_fscore: 91.5336, test_loss: 5.7506, test_acc: 70.6716, test_fscore: 70.7297, time: 19.81 sec
epoch: 89, train_loss: 4.1211, train_acc: 92.358, train_fscore: 92.3147, test_loss: 5.8602, test_acc: 70.0555, test_fscore: 69.9166, time: 20.07 sec
epoch: 90, train_loss: 4.1323, train_acc: 91.9277, train_fscore: 91.8686, test_loss: 5.8112, test_acc: 70.9797, test_fscore: 70.9092, time: 20.1 sec
----------best F-Score: 72.1053
              precision    recall  f1-score   support

           0     0.5417    0.7222    0.6190       144
           1     0.8440    0.7510    0.7948       245
           2     0.7085    0.7786    0.7419       384
           3     0.5625    0.7412    0.6396       170
           4     0.8147    0.7057    0.7563       299
           5     0.6916    0.5591    0.6183       381

    accuracy                         0.7006      1623
   macro avg     0.6938    0.7096    0.6950      1623
weighted avg     0.7145    0.7006    0.7019      1623

[[104   1   5   0  33   1]
 [  2 184  29   3   0  27]
 [ 25  16 299   4   9  31]
 [  0   3   7 126   0  34]
 [ 60   0  26   0 211   2]
 [  1  14  56  91   6 213]]
epoch: 91, train_loss: 4.1127, train_acc: 92.1859, train_fscore: 92.1181, test_loss: 5.8703, test_acc: 71.0413, test_fscore: 71.2904, time: 20.22 sec
epoch: 92, train_loss: 4.089, train_acc: 91.8072, train_fscore: 91.755, test_loss: 5.8461, test_acc: 71.1645, test_fscore: 71.151, time: 19.83 sec
epoch: 93, train_loss: 4.138, train_acc: 92.2719, train_fscore: 92.2236, test_loss: 5.8282, test_acc: 71.719, test_fscore: 71.7187, time: 19.95 sec
epoch: 94, train_loss: 4.0372, train_acc: 92.031, train_fscore: 91.9745, test_loss: 5.836, test_acc: 71.1645, test_fscore: 71.3609, time: 19.99 sec
epoch: 95, train_loss: 4.0039, train_acc: 92.0998, train_fscore: 92.0502, test_loss: 5.8665, test_acc: 70.1171, test_fscore: 70.0837, time: 22.67 sec
epoch: 96, train_loss: 4.0103, train_acc: 92.5129, train_fscore: 92.4786, test_loss: 5.7262, test_acc: 70.2403, test_fscore: 70.0736, time: 22.09 sec
epoch: 97, train_loss: 4.0375, train_acc: 92.4096, train_fscore: 92.3515, test_loss: 5.9794, test_acc: 70.0555, test_fscore: 70.1172, time: 21.18 sec

*** 新的最优F1分数: 73.0839% (Epoch 98) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 61.1111% (144 样本)
sad: 75.5102% (245 样本)
neutral: 81.2500% (384 样本)
angry: 67.6471% (170 样本)
excited: 78.9298% (299 样本)
frustrated: 65.8793% (381 样本)

每个类别的F1分数:
happy: 63.5379%
sad: 79.7414%
neutral: 74.7305%
angry: 67.6471%
excited: 79.0620%
frustrated: 68.4857%

总体指标:
总体准确率: 73.1362%
加权准确率 (W_ACC, 按测试集分布): 73.1362%
总体F1分数 (不加权宏平均): 72.2008%
手动计算宏平均F1: 72.2008%
加权F1 (W_F1, 按测试集分布): 73.0839%
============================================================
epoch: 98, train_loss: 3.9812, train_acc: 92.8916, train_fscore: 92.8412, test_loss: 5.8141, test_acc: 73.1362, test_fscore: 73.0839, time: 22.23 sec
epoch: 99, train_loss: 3.9477, train_acc: 93.2014, train_fscore: 93.1623, test_loss: 5.8397, test_acc: 71.411, test_fscore: 71.5272, time: 21.94 sec
epoch: 100, train_loss: 4.0026, train_acc: 93.2014, train_fscore: 93.1577, test_loss: 5.8142, test_acc: 70.7948, test_fscore: 70.8779, time: 21.88 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
epoch: 101, train_loss: 3.926, train_acc: 93.3563, train_fscore: 93.3075, test_loss: 5.8961, test_acc: 71.0413, test_fscore: 71.1593, time: 21.03 sec
epoch: 102, train_loss: 3.8868, train_acc: 93.4251, train_fscore: 93.3869, test_loss: 5.91, test_acc: 71.7807, test_fscore: 71.7657, time: 20.99 sec
epoch: 103, train_loss: 3.855, train_acc: 93.7866, train_fscore: 93.7507, test_loss: 5.9365, test_acc: 70.5484, test_fscore: 70.5829, time: 21.0 sec
epoch: 104, train_loss: 3.8679, train_acc: 93.7522, train_fscore: 93.7152, test_loss: 6.0591, test_acc: 71.719, test_fscore: 71.7334, time: 21.87 sec
epoch: 105, train_loss: 3.8297, train_acc: 93.3046, train_fscore: 93.2602, test_loss: 6.0436, test_acc: 70.9181, test_fscore: 70.9894, time: 21.8 sec
epoch: 106, train_loss: 3.8644, train_acc: 93.8038, train_fscore: 93.7695, test_loss: 6.0542, test_acc: 70.0555, test_fscore: 70.0239, time: 21.79 sec
epoch: 107, train_loss: 3.813, train_acc: 94.0275, train_fscore: 93.9853, test_loss: 5.9995, test_acc: 70.4251, test_fscore: 70.4865, time: 22.33 sec
epoch: 108, train_loss: 3.8084, train_acc: 93.6145, train_fscore: 93.5693, test_loss: 6.0352, test_acc: 70.7332, test_fscore: 70.7953, time: 21.15 sec
epoch: 109, train_loss: 3.7949, train_acc: 94.2857, train_fscore: 94.2558, test_loss: 6.0012, test_acc: 70.5484, test_fscore: 70.5137, time: 21.69 sec
epoch: 110, train_loss: 3.7968, train_acc: 94.0964, train_fscore: 94.0715, test_loss: 5.9167, test_acc: 71.3494, test_fscore: 71.5482, time: 21.28 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
epoch: 111, train_loss: 3.7926, train_acc: 94.8193, train_fscore: 94.7905, test_loss: 6.0294, test_acc: 71.1029, test_fscore: 71.1238, time: 21.06 sec
epoch: 112, train_loss: 3.7908, train_acc: 94.2857, train_fscore: 94.2599, test_loss: 6.12, test_acc: 70.61, test_fscore: 70.4849, time: 21.39 sec
epoch: 113, train_loss: 3.7671, train_acc: 94.5439, train_fscore: 94.5193, test_loss: 6.0697, test_acc: 70.5484, test_fscore: 70.5294, time: 21.57 sec
epoch: 114, train_loss: 3.7737, train_acc: 93.9587, train_fscore: 93.9146, test_loss: 5.9532, test_acc: 71.4726, test_fscore: 71.6036, time: 21.63 sec
epoch: 115, train_loss: 3.7786, train_acc: 94.0448, train_fscore: 94.0113, test_loss: 6.0406, test_acc: 71.9655, test_fscore: 72.0492, time: 21.57 sec
epoch: 116, train_loss: 3.7413, train_acc: 94.9225, train_fscore: 94.8941, test_loss: 6.1092, test_acc: 70.0555, test_fscore: 70.0621, time: 21.53 sec
epoch: 117, train_loss: 3.7085, train_acc: 94.8709, train_fscore: 94.8431, test_loss: 5.974, test_acc: 69.6242, test_fscore: 69.7283, time: 21.19 sec
epoch: 118, train_loss: 3.7002, train_acc: 94.957, train_fscore: 94.9283, test_loss: 6.0104, test_acc: 71.4726, test_fscore: 71.5133, time: 24.07 sec
epoch: 119, train_loss: 3.7088, train_acc: 95.2668, train_fscore: 95.2413, test_loss: 6.1424, test_acc: 70.9181, test_fscore: 70.9679, time: 21.81 sec
epoch: 120, train_loss: 3.6983, train_acc: 95.525, train_fscore: 95.503, test_loss: 6.129, test_acc: 70.4251, test_fscore: 70.479, time: 21.82 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
epoch: 121, train_loss: 3.6479, train_acc: 95.5766, train_fscore: 95.5566, test_loss: 6.2262, test_acc: 69.7474, test_fscore: 69.5702, time: 21.74 sec
epoch: 122, train_loss: 3.6577, train_acc: 94.5611, train_fscore: 94.5319, test_loss: 6.1517, test_acc: 70.0555, test_fscore: 69.9829, time: 21.47 sec
epoch: 123, train_loss: 3.6191, train_acc: 95.1635, train_fscore: 95.1375, test_loss: 6.2071, test_acc: 70.6716, test_fscore: 70.7292, time: 21.33 sec
epoch: 124, train_loss: 3.6528, train_acc: 95.6799, train_fscore: 95.6583, test_loss: 6.1675, test_acc: 70.4868, test_fscore: 70.581, time: 21.26 sec
epoch: 125, train_loss: 3.6071, train_acc: 95.6454, train_fscore: 95.6292, test_loss: 6.1271, test_acc: 70.1787, test_fscore: 70.2922, time: 21.3 sec
epoch: 126, train_loss: 3.6121, train_acc: 95.4905, train_fscore: 95.4679, test_loss: 6.0453, test_acc: 71.2877, test_fscore: 71.3199, time: 21.81 sec
epoch: 127, train_loss: 3.588, train_acc: 95.8864, train_fscore: 95.8683, test_loss: 6.1235, test_acc: 70.7948, test_fscore: 70.7245, time: 21.36 sec
epoch: 128, train_loss: 3.6001, train_acc: 95.7487, train_fscore: 95.7285, test_loss: 6.0577, test_acc: 70.4868, test_fscore: 70.5493, time: 21.79 sec
epoch: 129, train_loss: 3.5947, train_acc: 95.7143, train_fscore: 95.7001, test_loss: 6.2439, test_acc: 69.7474, test_fscore: 69.8994, time: 21.28 sec
epoch: 130, train_loss: 3.5555, train_acc: 95.3528, train_fscore: 95.3278, test_loss: 6.0761, test_acc: 71.1645, test_fscore: 71.3306, time: 21.72 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
epoch: 131, train_loss: 3.5548, train_acc: 95.7143, train_fscore: 95.6939, test_loss: 6.2767, test_acc: 71.5342, test_fscore: 71.4756, time: 21.64 sec
epoch: 132, train_loss: 3.5366, train_acc: 95.7315, train_fscore: 95.7157, test_loss: 6.1142, test_acc: 70.2403, test_fscore: 70.2888, time: 21.71 sec
epoch: 133, train_loss: 3.5106, train_acc: 95.7831, train_fscore: 95.7636, test_loss: 6.2109, test_acc: 70.3635, test_fscore: 70.4556, time: 21.22 sec
epoch: 134, train_loss: 3.4807, train_acc: 96.1274, train_fscore: 96.1113, test_loss: 6.2338, test_acc: 70.8564, test_fscore: 70.991, time: 21.61 sec
epoch: 135, train_loss: 3.5232, train_acc: 96.4372, train_fscore: 96.4192, test_loss: 6.1924, test_acc: 70.3635, test_fscore: 70.4506, time: 20.82 sec
epoch: 136, train_loss: 3.4703, train_acc: 96.5232, train_fscore: 96.5084, test_loss: 6.3043, test_acc: 70.1787, test_fscore: 70.1697, time: 20.44 sec
epoch: 137, train_loss: 3.4403, train_acc: 96.5749, train_fscore: 96.5608, test_loss: 6.2875, test_acc: 70.5484, test_fscore: 70.5382, time: 20.4 sec
epoch: 138, train_loss: 3.4585, train_acc: 96.6093, train_fscore: 96.6002, test_loss: 6.2314, test_acc: 70.4251, test_fscore: 70.3825, time: 20.4 sec
epoch: 139, train_loss: 3.4762, train_acc: 96.5577, train_fscore: 96.5435, test_loss: 6.4084, test_acc: 70.3635, test_fscore: 70.4523, time: 20.04 sec
epoch: 140, train_loss: 3.4489, train_acc: 96.6265, train_fscore: 96.6112, test_loss: 6.1348, test_acc: 72.7049, test_fscore: 72.7188, time: 20.11 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
epoch: 141, train_loss: 3.4352, train_acc: 96.6437, train_fscore: 96.6292, test_loss: 6.1996, test_acc: 71.0413, test_fscore: 71.1907, time: 20.05 sec
epoch: 142, train_loss: 3.4169, train_acc: 96.9363, train_fscore: 96.9261, test_loss: 6.443, test_acc: 70.1787, test_fscore: 70.2029, time: 20.17 sec
epoch: 143, train_loss: 3.3943, train_acc: 96.7986, train_fscore: 96.7871, test_loss: 6.2768, test_acc: 71.8423, test_fscore: 71.8758, time: 20.13 sec
epoch: 144, train_loss: 3.4048, train_acc: 96.2306, train_fscore: 96.2225, test_loss: 6.2894, test_acc: 70.8564, test_fscore: 70.9041, time: 20.04 sec
epoch: 145, train_loss: 3.4274, train_acc: 96.8158, train_fscore: 96.8044, test_loss: 6.2438, test_acc: 70.5484, test_fscore: 70.5352, time: 19.99 sec
epoch: 146, train_loss: 3.3645, train_acc: 96.833, train_fscore: 96.8231, test_loss: 6.4636, test_acc: 70.1171, test_fscore: 70.2005, time: 20.0 sec
epoch: 147, train_loss: 3.3627, train_acc: 97.1256, train_fscore: 97.1154, test_loss: 6.3392, test_acc: 72.0271, test_fscore: 71.9809, time: 20.18 sec
epoch: 148, train_loss: 3.4023, train_acc: 96.6093, train_fscore: 96.5969, test_loss: 6.1939, test_acc: 70.3019, test_fscore: 70.357, time: 19.99 sec
epoch: 149, train_loss: 3.3762, train_acc: 96.9707, train_fscore: 96.9606, test_loss: 6.3801, test_acc: 70.9181, test_fscore: 70.9801, time: 20.19 sec
epoch: 150, train_loss: 3.3444, train_acc: 97.2289, train_fscore: 97.2186, test_loss: 6.2037, test_acc: 70.9181, test_fscore: 70.9358, time: 20.8 sec
----------best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]
Test performance..
Best F-Score: 73.0839
              precision    recall  f1-score   support

           0     0.6617    0.6111    0.6354       144
           1     0.8447    0.7551    0.7974       245
           2     0.6918    0.8125    0.7473       384
           3     0.6765    0.6765    0.6765       170
           4     0.7919    0.7893    0.7906       299
           5     0.7131    0.6588    0.6849       381

    accuracy                         0.7314      1623
   macro avg     0.7299    0.7172    0.7220      1623
weighted avg     0.7340    0.7314    0.7308      1623

[[ 88   1  11   0  43   1]
 [  2 185  26   3   2  27]
 [ 13  16 312   4  11  28]
 [  0   3   9 115   0  43]
 [ 29   0  32   0 236   2]
 [  1  14  61  48   6 251]]

==================================================
DETAILED EVALUATION WITH WEIGHTS
==================================================

每个类别的F1分数:
happy: 63.5379%
sad: 79.7414%
neutral: 74.7305%
angry: 67.6471%
excited: 79.0620%
frustrated: 68.4857%

总体指标:
总体准确率: 73.1362%
加权准确率 (W_ACC, 按测试集分布): 73.1362%
总体F1分数 (不加权宏平均): 72.2008%
手动计算宏平均F1: 72.2008%
加权F1 (W_F1, 按测试集分布): 73.0839%
============================================================