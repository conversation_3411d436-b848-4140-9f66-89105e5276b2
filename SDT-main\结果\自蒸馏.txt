Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=1, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 1
total parameters: 79687704
training parameters: 79687704
epoch: 1, train_loss: 5.2717, train_acc: 19.02, train_fscore: 18.13, test_loss: 4.7758, test_acc: 27.79, test_fscore: 25.65, time: 17.38 sec
epoch: 2, train_loss: 4.3719, train_acc: 35.37, train_fscore: 34.8, test_loss: 3.9985, test_acc: 49.91, test_fscore: 46.93, time: 16.61 sec
epoch: 3, train_loss: 3.4632, train_acc: 53.24, train_fscore: 49.38, test_loss: 3.7073, test_acc: 52.99, test_fscore: 50.32, time: 16.61 sec
epoch: 4, train_loss: 3.0325, train_acc: 57.06, train_fscore: 55.89, test_loss: 3.1893, test_acc: 58.72, test_fscore: 59.57, time: 16.38 sec
epoch: 5, train_loss: 2.729, train_acc: 57.73, train_fscore: 55.39, test_loss: 2.8564, test_acc: 63.71, test_fscore: 63.62, time: 16.52 sec
epoch: 6, train_loss: 2.5625, train_acc: 63.01, train_fscore: 61.48, test_loss: 2.9641, test_acc: 62.85, test_fscore: 63.6, time: 16.46 sec
epoch: 7, train_loss: 2.3453, train_acc: 66.47, train_fscore: 65.91, test_loss: 2.8376, test_acc: 63.46, test_fscore: 64.08, time: 16.41 sec
epoch: 8, train_loss: 2.2294, train_acc: 67.4, train_fscore: 67.15, test_loss: 2.6622, test_acc: 65.62, test_fscore: 65.91, time: 16.58 sec
epoch: 9, train_loss: 2.1981, train_acc: 67.73, train_fscore: 67.03, test_loss: 2.6298, test_acc: 64.88, test_fscore: 65.89, time: 16.6 sec
epoch: 10, train_loss: 2.1508, train_acc: 68.52, train_fscore: 68.28, test_loss: 2.6151, test_acc: 67.22, test_fscore: 67.46, time: 16.08 sec
----------best F-Score: 67.46
              precision    recall  f1-score   support

           0     0.4153    0.5278    0.4648       144
           1     0.8359    0.6653    0.7409       245
           2     0.6553    0.6484    0.6518       384
           3     0.6095    0.7529    0.6737       170
           4     0.7891    0.8261    0.8072       299
           5     0.6667    0.5984    0.6307       381

    accuracy                         0.6722      1623
   macro avg     0.6620    0.6698    0.6615      1623
weighted avg     0.6838    0.6722    0.6746      1623

[[ 76   4  12   2  48   2]
 [ 12 163  37   2   0  31]
 [ 50  15 249  20   9  41]
 [  0   0   3 128   0  39]
 [ 43   0   8   0 247   1]
 [  2  13  71  58   9 228]]
epoch: 11, train_loss: 2.1007, train_acc: 69.23, train_fscore: 68.84, test_loss: 2.587, test_acc: 65.93, test_fscore: 66.5, time: 16.27 sec
epoch: 12, train_loss: 1.9885, train_acc: 69.47, train_fscore: 69.07, test_loss: 2.4624, test_acc: 66.17, test_fscore: 66.69, time: 16.43 sec
epoch: 13, train_loss: 1.8448, train_acc: 71.03, train_fscore: 70.72, test_loss: 2.4518, test_acc: 65.74, test_fscore: 66.57, time: 16.57 sec
epoch: 14, train_loss: 1.794, train_acc: 70.69, train_fscore: 70.31, test_loss: 2.4777, test_acc: 68.15, test_fscore: 68.42, time: 16.4 sec
epoch: 15, train_loss: 1.7595, train_acc: 72.27, train_fscore: 72.04, test_loss: 2.4811, test_acc: 65.87, test_fscore: 66.61, time: 16.4 sec
epoch: 16, train_loss: 1.7543, train_acc: 71.07, train_fscore: 70.66, test_loss: 2.3917, test_acc: 67.47, test_fscore: 67.85, time: 16.42 sec
epoch: 17, train_loss: 1.6745, train_acc: 72.94, train_fscore: 72.77, test_loss: 2.4239, test_acc: 64.94, test_fscore: 65.67, time: 16.57 sec
epoch: 18, train_loss: 1.6431, train_acc: 73.82, train_fscore: 73.7, test_loss: 2.3328, test_acc: 67.34, test_fscore: 67.88, time: 16.6 sec
epoch: 19, train_loss: 1.5943, train_acc: 73.75, train_fscore: 73.37, test_loss: 2.3493, test_acc: 67.47, test_fscore: 68.16, time: 16.69 sec
epoch: 20, train_loss: 1.5632, train_acc: 74.41, train_fscore: 74.18, test_loss: 2.2721, test_acc: 68.88, test_fscore: 69.31, time: 16.86 sec
----------best F-Score: 69.31
              precision    recall  f1-score   support

           0     0.4524    0.6597    0.5367       144
           1     0.8294    0.7143    0.7675       245
           2     0.7029    0.6406    0.6703       384
           3     0.6085    0.7588    0.6754       170
           4     0.8256    0.7759    0.8000       299
           5     0.6713    0.6325    0.6514       381

    accuracy                         0.6888      1623
   macro avg     0.6817    0.6970    0.6836      1623
weighted avg     0.7051    0.6888    0.6931      1623

[[ 95   6   4   5  33   1]
 [  7 175  33   1   0  29]
 [ 48  17 246  16   8  49]
 [  0   0   3 129   0  38]
 [ 55   0  11   0 232   1]
 [  5  13  53  61   8 241]]
epoch: 21, train_loss: 1.4381, train_acc: 76.04, train_fscore: 75.97, test_loss: 2.3376, test_acc: 67.53, test_fscore: 68.03, time: 16.79 sec
epoch: 22, train_loss: 1.4908, train_acc: 75.51, train_fscore: 75.27, test_loss: 2.2886, test_acc: 68.52, test_fscore: 69.16, time: 16.22 sec
epoch: 23, train_loss: 1.4565, train_acc: 76.52, train_fscore: 76.37, test_loss: 2.3157, test_acc: 67.96, test_fscore: 68.67, time: 15.99 sec
epoch: 24, train_loss: 1.3658, train_acc: 76.51, train_fscore: 76.37, test_loss: 2.2676, test_acc: 68.08, test_fscore: 68.58, time: 16.44 sec
epoch: 25, train_loss: 1.4148, train_acc: 76.33, train_fscore: 76.17, test_loss: 2.3282, test_acc: 68.45, test_fscore: 68.95, time: 19.1 sec
epoch: 26, train_loss: 1.3582, train_acc: 77.99, train_fscore: 77.9, test_loss: 2.2266, test_acc: 69.32, test_fscore: 69.94, time: 18.52 sec
epoch: 27, train_loss: 1.3026, train_acc: 78.16, train_fscore: 78.07, test_loss: 2.2438, test_acc: 68.7, test_fscore: 69.13, time: 17.54 sec
epoch: 28, train_loss: 1.2999, train_acc: 77.14, train_fscore: 76.97, test_loss: 2.2463, test_acc: 70.3, test_fscore: 70.75, time: 16.58 sec
epoch: 29, train_loss: 1.255, train_acc: 78.57, train_fscore: 78.48, test_loss: 2.3524, test_acc: 67.59, test_fscore: 68.14, time: 16.61 sec
epoch: 30, train_loss: 1.2714, train_acc: 78.18, train_fscore: 77.99, test_loss: 2.2834, test_acc: 69.5, test_fscore: 70.0, time: 17.17 sec
----------best F-Score: 70.75
              precision    recall  f1-score   support

           0     0.4698    0.7014    0.5627       144
           1     0.8519    0.7510    0.7983       245
           2     0.6813    0.6849    0.6831       384
           3     0.6776    0.7294    0.7025       170
           4     0.8284    0.7425    0.7831       299
           5     0.6958    0.6483    0.6712       381

    accuracy                         0.7030      1623
   macro avg     0.7008    0.7096    0.7001      1623
weighted avg     0.7184    0.7030    0.7075      1623

[[101   4   5   0  33   1]
 [  4 184  28   1   1  27]
 [ 51  16 263  12   6  36]
 [  0   0   3 124   0  43]
 [ 53   0  23   0 222   1]
 [  6  12  64  46   6 247]]
epoch: 31, train_loss: 1.2385, train_acc: 79.14, train_fscore: 79.05, test_loss: 2.2805, test_acc: 68.76, test_fscore: 69.35, time: 16.62 sec
epoch: 32, train_loss: 1.2296, train_acc: 79.05, train_fscore: 78.95, test_loss: 2.2435, test_acc: 69.93, test_fscore: 70.36, time: 16.61 sec
epoch: 33, train_loss: 1.1688, train_acc: 79.35, train_fscore: 79.21, test_loss: 2.2168, test_acc: 69.87, test_fscore: 70.37, time: 16.57 sec
epoch: 34, train_loss: 1.131, train_acc: 79.97, train_fscore: 79.8, test_loss: 2.3265, test_acc: 68.82, test_fscore: 69.43, time: 16.62 sec
epoch: 35, train_loss: 1.1298, train_acc: 80.64, train_fscore: 80.54, test_loss: 2.4178, test_acc: 69.62, test_fscore: 70.05, time: 16.58 sec
epoch: 36, train_loss: 1.1448, train_acc: 80.31, train_fscore: 80.2, test_loss: 2.3078, test_acc: 70.24, test_fscore: 70.63, time: 16.6 sec
epoch: 37, train_loss: 1.0732, train_acc: 81.2, train_fscore: 81.09, test_loss: 2.2411, test_acc: 69.81, test_fscore: 70.31, time: 16.82 sec
epoch: 38, train_loss: 1.0268, train_acc: 81.34, train_fscore: 81.26, test_loss: 2.3869, test_acc: 70.3, test_fscore: 70.79, time: 16.58 sec
epoch: 39, train_loss: 1.0533, train_acc: 81.19, train_fscore: 81.11, test_loss: 2.3294, test_acc: 69.38, test_fscore: 69.8, time: 16.59 sec
epoch: 40, train_loss: 0.9924, train_acc: 81.55, train_fscore: 81.43, test_loss: 2.3587, test_acc: 70.18, test_fscore: 70.58, time: 16.59 sec
----------best F-Score: 70.79
              precision    recall  f1-score   support

           0     0.4723    0.7708    0.5858       144
           1     0.8436    0.7265    0.7807       245
           2     0.6923    0.7031    0.6977       384
           3     0.6796    0.7235    0.7009       170
           4     0.8406    0.7057    0.7673       299
           5     0.6986    0.6509    0.6739       381

    accuracy                         0.7030      1623
   macro avg     0.7045    0.7134    0.7010      1623
weighted avg     0.7231    0.7030    0.7079      1623

[[111   4   3   0  26   0]
 [  3 178  31   3   1  29]
 [ 52  15 270   9   5  33]
 [  0   0   3 123   0  44]
 [ 65   1  21   0 211   1]
 [  4  13  62  46   8 248]]
epoch: 41, train_loss: 1.0264, train_acc: 82.56, train_fscore: 82.52, test_loss: 2.3084, test_acc: 70.24, test_fscore: 70.67, time: 16.6 sec
epoch: 42, train_loss: 1.0334, train_acc: 81.79, train_fscore: 81.65, test_loss: 2.3618, test_acc: 69.19, test_fscore: 69.67, time: 16.63 sec
epoch: 43, train_loss: 0.9284, train_acc: 82.77, train_fscore: 82.64, test_loss: 2.2945, test_acc: 69.93, test_fscore: 70.34, time: 16.78 sec
epoch: 44, train_loss: 0.9378, train_acc: 82.81, train_fscore: 82.7, test_loss: 2.3406, test_acc: 70.67, test_fscore: 71.02, time: 17.23 sec
epoch: 45, train_loss: 0.9112, train_acc: 82.75, train_fscore: 82.67, test_loss: 2.4845, test_acc: 69.5, test_fscore: 69.85, time: 17.42 sec
epoch: 46, train_loss: 0.9491, train_acc: 82.34, train_fscore: 82.19, test_loss: 2.3306, test_acc: 71.53, test_fscore: 71.96, time: 17.33 sec
epoch: 47, train_loss: 0.9102, train_acc: 83.08, train_fscore: 82.93, test_loss: 2.4432, test_acc: 69.07, test_fscore: 69.57, time: 17.4 sec
epoch: 48, train_loss: 0.9124, train_acc: 83.49, train_fscore: 83.42, test_loss: 2.3497, test_acc: 69.93, test_fscore: 70.29, time: 17.4 sec
epoch: 49, train_loss: 0.9449, train_acc: 83.37, train_fscore: 83.28, test_loss: 2.469, test_acc: 70.3, test_fscore: 70.55, time: 17.41 sec
epoch: 50, train_loss: 0.8949, train_acc: 83.98, train_fscore: 83.81, test_loss: 2.5018, test_acc: 69.69, test_fscore: 70.11, time: 17.17 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 51, train_loss: 0.8518, train_acc: 84.29, train_fscore: 84.21, test_loss: 2.4715, test_acc: 69.07, test_fscore: 69.44, time: 17.4 sec
epoch: 52, train_loss: 0.815, train_acc: 84.27, train_fscore: 84.17, test_loss: 2.4756, test_acc: 70.49, test_fscore: 70.85, time: 17.4 sec
epoch: 53, train_loss: 0.8001, train_acc: 84.53, train_fscore: 84.45, test_loss: 2.5014, test_acc: 70.43, test_fscore: 70.69, time: 17.21 sec
epoch: 54, train_loss: 0.7916, train_acc: 84.51, train_fscore: 84.41, test_loss: 2.5931, test_acc: 69.87, test_fscore: 70.2, time: 17.36 sec
epoch: 55, train_loss: 0.7532, train_acc: 85.2, train_fscore: 85.08, test_loss: 2.5834, test_acc: 70.36, test_fscore: 70.77, time: 17.19 sec
epoch: 56, train_loss: 0.7739, train_acc: 85.59, train_fscore: 85.51, test_loss: 2.6302, test_acc: 70.06, test_fscore: 70.35, time: 17.39 sec
epoch: 57, train_loss: 0.7286, train_acc: 85.23, train_fscore: 85.16, test_loss: 2.6087, test_acc: 69.69, test_fscore: 69.87, time: 17.62 sec
epoch: 58, train_loss: 0.7329, train_acc: 85.8, train_fscore: 85.66, test_loss: 2.5647, test_acc: 70.61, test_fscore: 70.92, time: 17.75 sec
epoch: 59, train_loss: 0.7291, train_acc: 85.7, train_fscore: 85.56, test_loss: 2.6797, test_acc: 70.18, test_fscore: 70.52, time: 17.19 sec
epoch: 60, train_loss: 0.7073, train_acc: 86.3, train_fscore: 86.23, test_loss: 2.6549, test_acc: 70.36, test_fscore: 70.64, time: 18.05 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 61, train_loss: 0.6727, train_acc: 86.14, train_fscore: 86.06, test_loss: 2.612, test_acc: 70.79, test_fscore: 71.08, time: 18.43 sec
epoch: 62, train_loss: 0.6441, train_acc: 86.47, train_fscore: 86.37, test_loss: 2.8081, test_acc: 69.25, test_fscore: 69.55, time: 18.35 sec
epoch: 63, train_loss: 0.6228, train_acc: 86.01, train_fscore: 85.9, test_loss: 2.7249, test_acc: 69.93, test_fscore: 70.14, time: 18.01 sec
epoch: 64, train_loss: 0.6504, train_acc: 86.37, train_fscore: 86.25, test_loss: 2.7656, test_acc: 69.62, test_fscore: 69.94, time: 17.95 sec
epoch: 65, train_loss: 0.6531, train_acc: 86.7, train_fscore: 86.61, test_loss: 2.7044, test_acc: 70.67, test_fscore: 70.88, time: 18.23 sec
epoch: 66, train_loss: 0.5825, train_acc: 87.5, train_fscore: 87.4, test_loss: 2.9162, test_acc: 69.93, test_fscore: 70.29, time: 18.4 sec
epoch: 67, train_loss: 0.5646, train_acc: 87.54, train_fscore: 87.44, test_loss: 2.7633, test_acc: 70.67, test_fscore: 70.94, time: 17.97 sec
epoch: 68, train_loss: 0.5788, train_acc: 87.47, train_fscore: 87.38, test_loss: 2.8419, test_acc: 70.3, test_fscore: 70.62, time: 18.39 sec
epoch: 69, train_loss: 0.6071, train_acc: 87.45, train_fscore: 87.35, test_loss: 2.8815, test_acc: 70.18, test_fscore: 70.42, time: 18.18 sec
epoch: 70, train_loss: 0.5615, train_acc: 87.95, train_fscore: 87.86, test_loss: 2.9001, test_acc: 70.43, test_fscore: 70.67, time: 16.82 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 71, train_loss: 0.5566, train_acc: 87.66, train_fscore: 87.57, test_loss: 2.7907, test_acc: 70.55, test_fscore: 70.82, time: 16.57 sec
epoch: 72, train_loss: 0.5329, train_acc: 87.92, train_fscore: 87.83, test_loss: 2.8872, test_acc: 69.87, test_fscore: 70.07, time: 18.15 sec
epoch: 73, train_loss: 0.568, train_acc: 88.06, train_fscore: 87.95, test_loss: 2.9015, test_acc: 70.92, test_fscore: 71.05, time: 18.2 sec
epoch: 74, train_loss: 0.5022, train_acc: 88.66, train_fscore: 88.56, test_loss: 3.0321, test_acc: 68.95, test_fscore: 69.31, time: 18.01 sec
epoch: 75, train_loss: 0.5352, train_acc: 88.43, train_fscore: 88.33, test_loss: 3.0274, test_acc: 69.99, test_fscore: 70.21, time: 18.12 sec
epoch: 76, train_loss: 0.5203, train_acc: 88.67, train_fscore: 88.61, test_loss: 2.9249, test_acc: 70.98, test_fscore: 71.06, time: 18.08 sec
epoch: 77, train_loss: 0.494, train_acc: 88.85, train_fscore: 88.77, test_loss: 3.0147, test_acc: 69.32, test_fscore: 69.57, time: 18.29 sec
epoch: 78, train_loss: 0.4639, train_acc: 88.83, train_fscore: 88.75, test_loss: 2.9988, test_acc: 70.61, test_fscore: 70.68, time: 18.13 sec
epoch: 79, train_loss: 0.469, train_acc: 88.85, train_fscore: 88.77, test_loss: 3.0787, test_acc: 69.62, test_fscore: 69.82, time: 17.94 sec
epoch: 80, train_loss: 0.4323, train_acc: 89.05, train_fscore: 88.95, test_loss: 3.0331, test_acc: 70.36, test_fscore: 70.57, time: 18.08 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 81, train_loss: 0.4128, train_acc: 89.38, train_fscore: 89.32, test_loss: 3.3142, test_acc: 69.56, test_fscore: 69.61, time: 18.12 sec
epoch: 82, train_loss: 0.4103, train_acc: 89.91, train_fscore: 89.84, test_loss: 3.0992, test_acc: 69.32, test_fscore: 69.44, time: 18.02 sec
epoch: 83, train_loss: 0.4175, train_acc: 89.64, train_fscore: 89.55, test_loss: 3.114, test_acc: 71.1, test_fscore: 71.27, time: 18.24 sec
epoch: 84, train_loss: 0.3927, train_acc: 89.6, train_fscore: 89.54, test_loss: 3.1486, test_acc: 69.56, test_fscore: 69.59, time: 18.05 sec
epoch: 85, train_loss: 0.3468, train_acc: 89.6, train_fscore: 89.52, test_loss: 3.3058, test_acc: 69.75, test_fscore: 69.92, time: 18.06 sec
epoch: 86, train_loss: 0.3552, train_acc: 89.97, train_fscore: 89.91, test_loss: 2.9789, test_acc: 71.41, test_fscore: 71.58, time: 17.98 sec
epoch: 87, train_loss: 0.3472, train_acc: 90.53, train_fscore: 90.46, test_loss: 3.2492, test_acc: 69.44, test_fscore: 69.56, time: 18.46 sec
epoch: 88, train_loss: 0.3324, train_acc: 89.88, train_fscore: 89.79, test_loss: 3.191, test_acc: 70.67, test_fscore: 70.75, time: 18.22 sec
epoch: 89, train_loss: 0.3296, train_acc: 90.41, train_fscore: 90.36, test_loss: 3.3335, test_acc: 69.69, test_fscore: 69.72, time: 18.11 sec
epoch: 90, train_loss: 0.3539, train_acc: 90.69, train_fscore: 90.62, test_loss: 3.3319, test_acc: 69.87, test_fscore: 70.04, time: 18.41 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 91, train_loss: 0.3346, train_acc: 90.29, train_fscore: 90.21, test_loss: 3.4007, test_acc: 69.93, test_fscore: 70.13, time: 18.05 sec
epoch: 92, train_loss: 0.3026, train_acc: 90.96, train_fscore: 90.89, test_loss: 3.3101, test_acc: 71.1, test_fscore: 71.28, time: 18.1 sec
epoch: 93, train_loss: 0.3134, train_acc: 90.96, train_fscore: 90.91, test_loss: 3.4656, test_acc: 70.43, test_fscore: 70.44, time: 18.09 sec
epoch: 94, train_loss: 0.3041, train_acc: 90.88, train_fscore: 90.81, test_loss: 3.4825, test_acc: 70.49, test_fscore: 70.66, time: 18.13 sec
epoch: 95, train_loss: 0.3043, train_acc: 91.53, train_fscore: 91.47, test_loss: 3.468, test_acc: 70.18, test_fscore: 70.35, time: 18.3 sec
epoch: 96, train_loss: 0.2899, train_acc: 91.2, train_fscore: 91.15, test_loss: 3.4895, test_acc: 70.43, test_fscore: 70.42, time: 18.16 sec
epoch: 97, train_loss: 0.2572, train_acc: 91.65, train_fscore: 91.6, test_loss: 3.4611, test_acc: 69.19, test_fscore: 69.24, time: 18.28 sec
epoch: 98, train_loss: 0.2773, train_acc: 91.27, train_fscore: 91.21, test_loss: 3.5987, test_acc: 69.56, test_fscore: 69.78, time: 18.4 sec
epoch: 99, train_loss: 0.263, train_acc: 91.67, train_fscore: 91.61, test_loss: 3.5734, test_acc: 70.12, test_fscore: 70.29, time: 18.04 sec
epoch: 100, train_loss: 0.3203, train_acc: 91.38, train_fscore: 91.33, test_loss: 3.6365, test_acc: 69.99, test_fscore: 69.99, time: 18.26 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 101, train_loss: 0.2695, train_acc: 91.31, train_fscore: 91.23, test_loss: 3.4443, test_acc: 69.38, test_fscore: 69.55, time: 18.12 sec
epoch: 102, train_loss: 0.2389, train_acc: 91.88, train_fscore: 91.84, test_loss: 3.4945, test_acc: 70.12, test_fscore: 70.05, time: 18.21 sec
epoch: 103, train_loss: 0.2236, train_acc: 91.39, train_fscore: 91.32, test_loss: 3.6399, test_acc: 69.75, test_fscore: 69.85, time: 18.06 sec
epoch: 104, train_loss: 0.2046, train_acc: 92.05, train_fscore: 91.99, test_loss: 3.7079, test_acc: 69.13, test_fscore: 69.2, time: 18.28 sec
epoch: 105, train_loss: 0.2162, train_acc: 92.22, train_fscore: 92.17, test_loss: 3.5751, test_acc: 69.99, test_fscore: 70.02, time: 17.92 sec
epoch: 106, train_loss: 0.2094, train_acc: 92.07, train_fscore: 91.99, test_loss: 3.657, test_acc: 70.06, test_fscore: 70.22, time: 18.06 sec
epoch: 107, train_loss: 0.1884, train_acc: 92.58, train_fscore: 92.55, test_loss: 3.609, test_acc: 70.06, test_fscore: 70.12, time: 18.0 sec
epoch: 108, train_loss: 0.1773, train_acc: 92.36, train_fscore: 92.31, test_loss: 3.7424, test_acc: 69.87, test_fscore: 69.93, time: 18.02 sec
epoch: 109, train_loss: 0.1606, train_acc: 92.65, train_fscore: 92.61, test_loss: 3.7014, test_acc: 71.23, test_fscore: 71.34, time: 18.28 sec
epoch: 110, train_loss: 0.1568, train_acc: 92.63, train_fscore: 92.58, test_loss: 3.7749, test_acc: 70.43, test_fscore: 70.58, time: 18.24 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 111, train_loss: 0.1624, train_acc: 92.98, train_fscore: 92.93, test_loss: 3.8111, test_acc: 70.67, test_fscore: 70.76, time: 18.27 sec
epoch: 112, train_loss: 0.1525, train_acc: 92.7, train_fscore: 92.64, test_loss: 3.8986, test_acc: 71.1, test_fscore: 71.21, time: 18.15 sec
epoch: 113, train_loss: 0.1527, train_acc: 92.94, train_fscore: 92.89, test_loss: 3.9235, test_acc: 69.25, test_fscore: 69.35, time: 18.19 sec
epoch: 114, train_loss: 0.1117, train_acc: 93.3, train_fscore: 93.26, test_loss: 3.9315, test_acc: 71.1, test_fscore: 71.23, time: 17.91 sec
epoch: 115, train_loss: 0.0993, train_acc: 93.15, train_fscore: 93.1, test_loss: 4.196, test_acc: 68.82, test_fscore: 68.9, time: 17.96 sec
epoch: 116, train_loss: 0.1428, train_acc: 93.63, train_fscore: 93.59, test_loss: 3.8439, test_acc: 70.79, test_fscore: 71.05, time: 18.18 sec
epoch: 117, train_loss: 0.1135, train_acc: 93.7, train_fscore: 93.66, test_loss: 3.9644, test_acc: 70.06, test_fscore: 70.15, time: 18.06 sec
epoch: 118, train_loss: 0.1056, train_acc: 93.17, train_fscore: 93.12, test_loss: 3.8515, test_acc: 70.92, test_fscore: 71.01, time: 18.07 sec
epoch: 119, train_loss: 0.1183, train_acc: 93.43, train_fscore: 93.38, test_loss: 4.109, test_acc: 68.95, test_fscore: 69.05, time: 18.09 sec
epoch: 120, train_loss: 0.0736, train_acc: 93.55, train_fscore: 93.5, test_loss: 4.1452, test_acc: 70.06, test_fscore: 70.17, time: 18.14 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 121, train_loss: 0.0904, train_acc: 94.18, train_fscore: 94.15, test_loss: 3.9153, test_acc: 70.3, test_fscore: 70.4, time: 18.25 sec
epoch: 122, train_loss: 0.1102, train_acc: 93.89, train_fscore: 93.85, test_loss: 3.9041, test_acc: 69.87, test_fscore: 70.07, time: 18.13 sec
epoch: 123, train_loss: 0.1172, train_acc: 93.43, train_fscore: 93.39, test_loss: 3.9249, test_acc: 69.99, test_fscore: 70.06, time: 18.04 sec
epoch: 124, train_loss: 0.0464, train_acc: 93.91, train_fscore: 93.87, test_loss: 3.9164, test_acc: 69.81, test_fscore: 69.87, time: 18.18 sec
epoch: 125, train_loss: 0.0338, train_acc: 93.6, train_fscore: 93.56, test_loss: 4.0798, test_acc: 69.56, test_fscore: 69.62, time: 18.12 sec
epoch: 126, train_loss: 0.0401, train_acc: 93.96, train_fscore: 93.92, test_loss: 4.2846, test_acc: 69.01, test_fscore: 68.98, time: 18.21 sec
epoch: 127, train_loss: 0.0877, train_acc: 93.82, train_fscore: 93.78, test_loss: 4.2574, test_acc: 69.32, test_fscore: 69.37, time: 18.26 sec
epoch: 128, train_loss: 0.0404, train_acc: 94.61, train_fscore: 94.58, test_loss: 3.9937, test_acc: 70.43, test_fscore: 70.45, time: 18.11 sec
epoch: 129, train_loss: 0.0195, train_acc: 94.22, train_fscore: 94.17, test_loss: 4.3066, test_acc: 69.13, test_fscore: 69.28, time: 17.97 sec
epoch: 130, train_loss: 0.0186, train_acc: 94.32, train_fscore: 94.29, test_loss: 4.0964, test_acc: 70.79, test_fscore: 70.89, time: 18.0 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 131, train_loss: -0.0116, train_acc: 94.7, train_fscore: 94.67, test_loss: 4.3001, test_acc: 68.7, test_fscore: 68.75, time: 18.01 sec
epoch: 132, train_loss: 0.015, train_acc: 94.49, train_fscore: 94.46, test_loss: 4.188, test_acc: 69.69, test_fscore: 69.79, time: 18.39 sec
epoch: 133, train_loss: 0.0096, train_acc: 94.87, train_fscore: 94.84, test_loss: 4.1486, test_acc: 69.32, test_fscore: 69.42, time: 18.11 sec
epoch: 134, train_loss: 0.0154, train_acc: 94.44, train_fscore: 94.42, test_loss: 4.3969, test_acc: 69.75, test_fscore: 69.72, time: 18.03 sec
epoch: 135, train_loss: 0.0113, train_acc: 94.87, train_fscore: 94.84, test_loss: 4.1503, test_acc: 69.5, test_fscore: 69.54, time: 18.1 sec
epoch: 136, train_loss: 0.0062, train_acc: 94.96, train_fscore: 94.93, test_loss: 4.1814, test_acc: 70.61, test_fscore: 70.58, time: 17.95 sec
epoch: 137, train_loss: -0.0256, train_acc: 95.06, train_fscore: 95.03, test_loss: 4.2138, test_acc: 69.81, test_fscore: 69.89, time: 18.01 sec
epoch: 138, train_loss: -0.0118, train_acc: 95.13, train_fscore: 95.11, test_loss: 4.2998, test_acc: 70.86, test_fscore: 71.0, time: 18.11 sec
epoch: 139, train_loss: -0.0362, train_acc: 95.18, train_fscore: 95.16, test_loss: 4.5414, test_acc: 69.62, test_fscore: 69.66, time: 18.25 sec
epoch: 140, train_loss: -0.0368, train_acc: 95.09, train_fscore: 95.07, test_loss: 4.5656, test_acc: 70.55, test_fscore: 70.76, time: 18.12 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
epoch: 141, train_loss: -0.0058, train_acc: 95.06, train_fscore: 95.04, test_loss: 4.4522, test_acc: 69.93, test_fscore: 70.05, time: 17.9 sec
epoch: 142, train_loss: -0.0281, train_acc: 94.94, train_fscore: 94.91, test_loss: 4.6555, test_acc: 69.25, test_fscore: 69.37, time: 18.06 sec
epoch: 143, train_loss: -0.0167, train_acc: 95.39, train_fscore: 95.36, test_loss: 4.5492, test_acc: 69.93, test_fscore: 70.18, time: 17.89 sec
epoch: 144, train_loss: -0.0127, train_acc: 95.66, train_fscore: 95.64, test_loss: 4.6165, test_acc: 70.61, test_fscore: 70.71, time: 17.99 sec
epoch: 145, train_loss: -0.0347, train_acc: 95.61, train_fscore: 95.59, test_loss: 4.348, test_acc: 70.24, test_fscore: 70.37, time: 18.0 sec
epoch: 146, train_loss: -0.0525, train_acc: 95.73, train_fscore: 95.71, test_loss: 4.5924, test_acc: 68.76, test_fscore: 68.9, time: 18.02 sec
epoch: 147, train_loss: -0.0596, train_acc: 95.97, train_fscore: 95.96, test_loss: 4.5372, test_acc: 70.3, test_fscore: 70.52, time: 17.85 sec
epoch: 148, train_loss: -0.0849, train_acc: 95.11, train_fscore: 95.09, test_loss: 4.671, test_acc: 70.3, test_fscore: 70.38, time: 18.11 sec
epoch: 149, train_loss: -0.0759, train_acc: 95.8, train_fscore: 95.78, test_loss: 4.5977, test_acc: 69.56, test_fscore: 69.62, time: 18.19 sec
epoch: 150, train_loss: -0.0861, train_acc: 95.7, train_fscore: 95.68, test_loss: 4.8152, test_acc: 70.36, test_fscore: 70.48, time: 17.94 sec
----------best F-Score: 71.96
              precision    recall  f1-score   support

           0     0.4891    0.7778    0.6005       144
           1     0.8664    0.7673    0.8139       245
           2     0.7037    0.6927    0.6982       384
           3     0.7052    0.7176    0.7114       170
           4     0.8271    0.7358    0.7788       299
           5     0.7028    0.6640    0.6829       381

    accuracy                         0.7153      1623
   macro avg     0.7157    0.7259    0.7143      1623
weighted avg     0.7319    0.7153    0.7196      1623

[[112   1   2   0  28   1]
 [  4 188  22   2   3  26]
 [ 51  16 266   9   7  35]
 [  0   0   4 122   0  44]
 [ 57   0  21   0 220   1]
 [  5  12  63  40   8 253]]
Traceback (most recent call last):
  File "e:\Pycharm WorkingSpace\SDT-main\SDT-main\train.py", line 337, in <module>
    if not args.testing:
AttributeError: 'Namespace' object has no attribute 'testing'