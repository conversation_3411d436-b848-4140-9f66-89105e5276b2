D:\Anaconda\envs\ljp\python.exe "E:\Pycharm WorkingSpace\MMERC\SDT-main\train.py"
Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=1, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 1
total parameters: 79687704
training parameters: 79687704

*** 新的最优F1分数: 22.3254% (Epoch 1) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 0.0000% (144 样本)
sad: 44.8980% (245 样本)
neutral: 11.4583% (384 样本)
angry: 45.8824% (170 样本)
excited: 0.0000% (299 样本)
frustrated: 50.6562% (381 样本)

每个类别的F1分数:
happy: 0.0000%
sad: 52.7578%
neutral: 15.6028%
angry: 20.6349%
excited: 0.0000%
frustrated: 36.2441%

总体指标:
总体准确率: 26.1861%
加权准确率 (W_ACC, 按测试集分布): 26.1861%
总体F1分数 (不加权宏平均): 20.8733%
手动计算宏平均F1: 20.8733%
加权F1 (W_F1, 按测试集分布): 22.3254%
============================================================
epoch: 1, train_loss: 20.7327, train_acc: 15.3701, train_fscore: 13.1212, test_loss: 20.9881, test_acc: 26.1861, test_fscore: 22.3254, time: 18.02 sec

*** 新的最优F1分数: 34.1787% (Epoch 2) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 16.6667% (144 样本)
sad: 65.7143% (245 样本)
neutral: 0.2604% (384 样本)
angry: 45.8824% (170 样本)
excited: 44.8161% (299 样本)
frustrated: 56.6929% (381 样本)

每个类别的F1分数:
happy: 21.1454%
sad: 60.1869%
neutral: 0.5013%
angry: 26.6212%
excited: 56.5401%
frustrated: 42.1463%

总体指标:
总体准确率: 37.8312%
加权准确率 (W_ACC, 按测试集分布): 37.8312%
总体F1分数 (不加权宏平均): 34.5235%
手动计算宏平均F1: 34.5235%
加权F1 (W_F1, 按测试集分布): 34.1787%
============================================================
epoch: 2, train_loss: 20.3882, train_acc: 30.327, train_fscore: 26.747, test_loss: 21.0308, test_acc: 37.8312, test_fscore: 34.1787, time: 18.74 sec

*** 新的最优F1分数: 41.4819% (Epoch 3) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 4.1667% (144 样本)
sad: 82.4490% (245 样本)
neutral: 36.7188% (384 样本)
angry: 85.2941% (170 样本)
excited: 85.9532% (299 样本)
frustrated: 5.5118% (381 样本)

每个类别的F1分数:
happy: 6.6667%
sad: 76.2264%
neutral: 41.2884%
angry: 41.1348%
excited: 70.2186%
frustrated: 10.0962%

总体指标:
总体准确率: 47.5662%
加权准确率 (W_ACC, 按测试集分布): 47.5662%
总体F1分数 (不加权宏平均): 40.9385%
手动计算宏平均F1: 40.9385%
加权F1 (W_F1, 按测试集分布): 41.4819%
============================================================
epoch: 3, train_loss: 19.8921, train_acc: 46.0585, train_fscore: 42.3125, test_loss: 20.0062, test_acc: 47.5662, test_fscore: 41.4819, time: 17.43 sec

*** 新的最优F1分数: 54.7693% (Epoch 4) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 38.8889% (144 样本)
sad: 71.4286% (245 样本)
neutral: 17.4479% (384 样本)
angry: 61.1765% (170 样本)
excited: 79.9331% (299 样本)
frustrated: 73.4908% (381 样本)

每个类别的F1分数:
happy: 31.2849%
sad: 73.2218%
neutral: 25.1880%
angry: 62.4625%
excited: 77.4716%
frustrated: 60.3448%

总体指标:
总体准确率: 56.7468%
加权准确率 (W_ACC, 按测试集分布): 56.7468%
总体F1分数 (不加权宏平均): 54.9956%
手动计算宏平均F1: 54.9956%
加权F1 (W_F1, 按测试集分布): 54.7693%
============================================================
epoch: 4, train_loss: 19.1089, train_acc: 51.6695, train_fscore: 48.9141, test_loss: 19.8699, test_acc: 56.7468, test_fscore: 54.7693, time: 17.43 sec

*** 新的最优F1分数: 56.2484% (Epoch 5) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 41.6667% (144 样本)
sad: 74.2857% (245 样本)
neutral: 56.7708% (384 样本)
angry: 83.5294% (170 样本)
excited: 67.2241% (299 样本)
frustrated: 30.9711% (381 样本)

每个类别的F1分数:
happy: 35.3982%
sad: 72.5100%
neutral: 54.9811%
angry: 58.4362%
excited: 72.1724%
frustrated: 41.4763%

总体指标:
总体准确率: 56.7468%
加权准确率 (W_ACC, 按测试集分布): 56.7468%
总体F1分数 (不加权宏平均): 55.8290%
手动计算宏平均F1: 55.8290%
加权F1 (W_F1, 按测试集分布): 56.2484%
============================================================
epoch: 5, train_loss: 19.1177, train_acc: 60.7229, train_fscore: 59.5521, test_loss: 19.6395, test_acc: 56.7468, test_fscore: 56.2484, time: 18.38 sec

*** 新的最优F1分数: 64.5617% (Epoch 6) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 48.6111% (144 样本)
sad: 73.0612% (245 样本)
neutral: 59.1146% (384 样本)
angry: 64.1176% (170 样本)
excited: 74.2475% (299 样本)
frustrated: 61.4173% (381 样本)

每个类别的F1分数:
happy: 41.1765%
sad: 74.7390%
neutral: 60.8579%
angry: 64.4970%
excited: 75.7679%
frustrated: 61.8230%

总体指标:
总体准确率: 64.1405%
加权准确率 (W_ACC, 按测试集分布): 64.1405%
总体F1分数 (不加权宏平均): 63.1436%
手动计算宏平均F1: 63.1436%
加权F1 (W_F1, 按测试集分布): 64.5617%
============================================================
epoch: 6, train_loss: 19.1762, train_acc: 60.0344, train_fscore: 57.2035, test_loss: 22.5666, test_acc: 64.1405, test_fscore: 64.5617, time: 19.16 sec

*** 新的最优F1分数: 65.0589% (Epoch 7) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 50.6944% (144 样本)
sad: 72.6531% (245 样本)
neutral: 58.0729% (384 样本)
angry: 77.6471% (170 样本)
excited: 72.5753% (299 样本)
frustrated: 59.3176% (381 样本)

每个类别的F1分数:
happy: 42.3188%
sad: 75.2643%
neutral: 61.0959%
angry: 66.6667%
excited: 75.6098%
frustrated: 62.0879%

总体指标:
总体准确率: 64.6334%
加权准确率 (W_ACC, 按测试集分布): 64.6334%
总体F1分数 (不加权宏平均): 63.8406%
手动计算宏平均F1: 63.8406%
加权F1 (W_F1, 按测试集分布): 65.0589%
============================================================
epoch: 7, train_loss: 19.7903, train_acc: 64.9225, train_fscore: 64.5226, test_loss: 22.3322, test_acc: 64.6334, test_fscore: 65.0589, time: 18.98 sec

*** 新的最优F1分数: 65.7143% (Epoch 8) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 45.1389% (144 样本)
sad: 71.0204% (245 样本)
neutral: 57.8125% (384 样本)
angry: 75.2941% (170 样本)
excited: 77.9264% (299 样本)
frustrated: 62.9921% (381 样本)

每个类别的F1分数:
happy: 40.3727%
sad: 75.3247%
neutral: 61.6667%
angry: 66.4935%
excited: 78.0570%
frustrated: 63.1579%

总体指标:
总体准确率: 65.4344%
加权准确率 (W_ACC, 按测试集分布): 65.4344%
总体F1分数 (不加权宏平均): 64.1787%
手动计算宏平均F1: 64.1787%
加权F1 (W_F1, 按测试集分布): 65.7143%
============================================================
epoch: 8, train_loss: 19.976, train_acc: 65.0602, train_fscore: 64.414, test_loss: 20.8271, test_acc: 65.4344, test_fscore: 65.7143, time: 18.13 sec
epoch: 9, train_loss: 20.5599, train_acc: 66.8503, train_fscore: 66.0038, test_loss: 19.8043, test_acc: 64.7566, test_fscore: 65.3639, time: 18.26 sec

*** 新的最优F1分数: 66.3402% (Epoch 10) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.1667% (144 样本)
sad: 70.2041% (245 样本)
neutral: 64.5833% (384 样本)
angry: 70.5882% (170 样本)
excited: 72.9097% (299 样本)
frustrated: 60.6299% (381 样本)

每个类别的F1分数:
happy: 43.9437%
sad: 75.2735%
neutral: 65.0066%
angry: 65.2174%
excited: 75.8261%
frustrated: 63.4615%

总体指标:
总体准确率: 65.7425%
加权准确率 (W_ACC, 按测试集分布): 65.7425%
总体F1分数 (不加权宏平均): 64.7881%
手动计算宏平均F1: 64.7881%
加权F1 (W_F1, 按测试集分布): 66.3402%
============================================================
epoch: 10, train_loss: 18.8723, train_acc: 65.6454, train_fscore: 65.1631, test_loss: 19.9372, test_acc: 65.7425, test_fscore: 66.3402, time: 17.74 sec
----------best F-Score: 66.3402
              precision    recall  f1-score   support

           0     0.3697    0.5417    0.4394       144
           1     0.8113    0.7020    0.7527       245
           2     0.6544    0.6458    0.6501       384
           3     0.6061    0.7059    0.6522       170
           4     0.7899    0.7291    0.7583       299
           5     0.6657    0.6063    0.6346       381

    accuracy                         0.6574      1623
   macro avg     0.6495    0.6551    0.6479      1623
weighted avg     0.6754    0.6574    0.6634      1623

[[ 78   5  13   2  45   1]
 [  9 172  32   2   0  30]
 [ 55  16 248  20   8  37]
 [  0   0   3 120   0  47]
 [ 65   0  15   0 218   1]
 [  4  19  68  54   5 231]]
epoch: 11, train_loss: 18.0081, train_acc: 68.7435, train_fscore: 68.2399, test_loss: 18.5623, test_acc: 64.8799, test_fscore: 65.5404, time: 17.77 sec

*** 新的最优F1分数: 66.7112% (Epoch 12) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 56.9444% (144 样本)
sad: 70.2041% (245 样本)
neutral: 58.8542% (384 样本)
angry: 82.3529% (170 样本)
excited: 72.9097% (299 样本)
frustrated: 61.9423% (381 样本)

每个类别的F1分数:
happy: 45.8101%
sad: 75.7709%
neutral: 63.1285%
angry: 68.2927%
excited: 76.8959%
frustrated: 63.6977%

总体指标:
总体准确率: 66.1738%
加权准确率 (W_ACC, 按测试集分布): 66.1738%
总体F1分数 (不加权宏平均): 65.5993%
手动计算宏平均F1: 65.5993%
加权F1 (W_F1, 按测试集分布): 66.7112%
============================================================
epoch: 12, train_loss: 16.5029, train_acc: 67.9346, train_fscore: 67.7806, test_loss: 16.4952, test_acc: 66.1738, test_fscore: 66.7112, time: 16.56 sec
epoch: 13, train_loss: 13.7627, train_acc: 69.6213, train_fscore: 69.3624, test_loss: 14.5507, test_acc: 65.8657, test_fscore: 66.4547, time: 15.98 sec

*** 新的最优F1分数: 67.0695% (Epoch 14) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.1944% (144 样本)
sad: 71.4286% (245 样本)
neutral: 62.2396% (384 样本)
angry: 73.5294% (170 样本)
excited: 68.5619% (299 样本)
frustrated: 63.7795% (381 样本)

每个类别的F1分数:
happy: 48.5333%
sad: 75.5940%
neutral: 65.4795%
angry: 66.4894%
excited: 74.2754%
frustrated: 64.8000%

总体指标:
总体准确率: 66.4202%
加权准确率 (W_ACC, 按测试集分布): 66.4202%
总体F1分数 (不加权宏平均): 65.8619%
手动计算宏平均F1: 65.8619%
加权F1 (W_F1, 按测试集分布): 67.0695%
============================================================
epoch: 14, train_loss: 12.7239, train_acc: 70.1205, train_fscore: 69.6682, test_loss: 14.6547, test_acc: 66.4202, test_fscore: 67.0695, time: 16.0 sec
epoch: 15, train_loss: 13.0533, train_acc: 71.2565, train_fscore: 71.0359, test_loss: 14.6985, test_acc: 65.0647, test_fscore: 65.5289, time: 15.98 sec

*** 新的最优F1分数: 67.5271% (Epoch 16) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 56.9444% (144 样本)
sad: 65.3061% (245 样本)
neutral: 64.5833% (384 样本)
angry: 77.0588% (170 样本)
excited: 77.9264% (299 样本)
frustrated: 61.4173% (381 样本)

每个类别的F1分数:
happy: 47.1264%
sad: 74.2459%
neutral: 65.6954%
angry: 67.0077%
excited: 78.8494%
frustrated: 64.1096%

总体指标:
总体准确率: 67.0364%
加权准确率 (W_ACC, 按测试集分布): 67.0364%
总体F1分数 (不加权宏平均): 66.1724%
手动计算宏平均F1: 66.1724%
加权F1 (W_F1, 按测试集分布): 67.5271%
============================================================
epoch: 16, train_loss: 15.3738, train_acc: 70.3614, train_fscore: 69.905, test_loss: 20.1262, test_acc: 67.0364, test_fscore: 67.5271, time: 16.04 sec

*** 新的最优F1分数: 67.9466% (Epoch 17) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 61.8056% (144 样本)
sad: 82.8571% (245 样本)
neutral: 55.9896% (384 样本)
angry: 70.5882% (170 样本)
excited: 67.5585% (299 样本)
frustrated: 70.0787% (381 样本)

每个类别的F1分数:
happy: 48.3696%
sad: 80.0789%
neutral: 64.1791%
angry: 67.0391%
excited: 73.9927%
frustrated: 67.0013%

总体指标:
总体准确率: 67.5293%
加权准确率 (W_ACC, 按测试集分布): 67.5293%
总体F1分数 (不加权宏平均): 66.7768%
手动计算宏平均F1: 66.7768%
加权F1 (W_F1, 按测试集分布): 67.9466%
============================================================
epoch: 17, train_loss: 16.8711, train_acc: 72.8744, train_fscore: 72.7662, test_loss: 20.9504, test_acc: 67.5293, test_fscore: 67.9466, time: 15.97 sec

*** 新的最优F1分数: 68.0210% (Epoch 18) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.8889% (144 样本)
sad: 68.5714% (245 样本)
neutral: 61.9792% (384 样本)
angry: 73.5294% (170 样本)
excited: 71.9064% (299 样本)
frustrated: 66.9291% (381 样本)

每个类别的F1分数:
happy: 49.7297%
sad: 76.1905%
neutral: 65.5647%
angry: 66.3130%
excited: 76.6488%
frustrated: 66.1479%

总体指标:
总体准确率: 67.3444%
加权准确率 (W_ACC, 按测试集分布): 67.3444%
总体F1分数 (不加权宏平均): 66.7658%
手动计算宏平均F1: 66.7658%
加权F1 (W_F1, 按测试集分布): 68.0210%
============================================================
epoch: 18, train_loss: 19.6943, train_acc: 70.6713, train_fscore: 70.0296, test_loss: 20.3449, test_acc: 67.3444, test_fscore: 68.021, time: 15.98 sec
epoch: 19, train_loss: 18.8266, train_acc: 72.2203, train_fscore: 72.028, test_loss: 22.7703, test_acc: 66.3586, test_fscore: 66.7077, time: 15.78 sec

*** 新的最优F1分数: 68.6632% (Epoch 20) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 64.5833% (144 样本)
sad: 66.5306% (245 样本)
neutral: 66.6667% (384 样本)
angry: 72.9412% (170 样本)
excited: 71.2375% (299 样本)
frustrated: 66.4042% (381 样本)

每个类别的F1分数:
happy: 49.0765%
sad: 75.2887%
neutral: 67.4572%
angry: 68.3196%
excited: 76.6187%
frustrated: 66.9312%

总体指标:
总体准确率: 67.8990%
加权准确率 (W_ACC, 按测试集分布): 67.8990%
总体F1分数 (不加权宏平均): 67.2820%
手动计算宏平均F1: 67.2820%
加权F1 (W_F1, 按测试集分布): 68.6632%
============================================================
epoch: 20, train_loss: 20.9468, train_acc: 71.9277, train_fscore: 71.4656, test_loss: 18.3434, test_acc: 67.899, test_fscore: 68.6632, time: 15.79 sec
----------best F-Score: 68.6632
              precision    recall  f1-score   support

           0     0.3957    0.6458    0.4908       144
           1     0.8670    0.6653    0.7529       245
           2     0.6827    0.6667    0.6746       384
           3     0.6425    0.7294    0.6832       170
           4     0.8288    0.7124    0.7662       299
           5     0.6747    0.6640    0.6693       381

    accuracy                         0.6790      1623
   macro avg     0.6819    0.6806    0.6728      1623
weighted avg     0.7059    0.6790    0.6866      1623

[[ 93   2  12   0  36   1]
 [ 14 163  32   2   0  34]
 [ 51  14 256  17   3  43]
 [  0   0   3 124   0  43]
 [ 75   0  10   0 213   1]
 [  2   9  62  50   5 253]]
epoch: 21, train_loss: 19.9398, train_acc: 73.6489, train_fscore: 73.5234, test_loss: 20.4357, test_acc: 68.0838, test_fscore: 68.4554, time: 15.99 sec

*** 新的最优F1分数: 69.0124% (Epoch 22) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.1944% (144 样本)
sad: 71.4286% (245 样本)
neutral: 67.1875% (384 样本)
angry: 71.1765% (170 样本)
excited: 67.8930% (299 样本)
frustrated: 68.7664% (381 样本)

每个类别的F1分数:
happy: 50.9804%
sad: 78.2998%
neutral: 67.8055%
angry: 67.2222%
excited: 74.7698%
frustrated: 67.3522%

总体指标:
总体准确率: 68.3919%
加权准确率 (W_ACC, 按测试集分布): 68.3919%
总体F1分数 (不加权宏平均): 67.7383%
手动计算宏平均F1: 67.7383%
加权F1 (W_F1, 按测试集分布): 69.0124%
============================================================
epoch: 22, train_loss: 17.9504, train_acc: 72.9088, train_fscore: 72.5908, test_loss: 19.3525, test_acc: 68.3919, test_fscore: 69.0124, time: 16.0 sec

*** 新的最优F1分数: 69.3531% (Epoch 23) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 66.6667% (144 样本)
sad: 77.5510% (245 样本)
neutral: 61.1979% (384 样本)
angry: 77.0588% (170 样本)
excited: 71.9064% (299 样本)
frustrated: 65.3543% (381 样本)

每个类别的F1分数:
happy: 51.2000%
sad: 80.1688%
neutral: 67.1429%
angry: 68.2292%
excited: 76.9231%
frustrated: 66.0477%

总体指标:
总体准确率: 68.7616%
加权准确率 (W_ACC, 按测试集分布): 68.7616%
总体F1分数 (不加权宏平均): 68.2853%
手动计算宏平均F1: 68.2853%
加权F1 (W_F1, 按测试集分布): 69.3531%
============================================================
epoch: 23, train_loss: 18.6608, train_acc: 73.9759, train_fscore: 73.8811, test_loss: 19.6256, test_acc: 68.7616, test_fscore: 69.3531, time: 15.99 sec

*** 新的最优F1分数: 70.1618% (Epoch 24) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 59.0278% (144 样本)
sad: 75.5102% (245 样本)
neutral: 62.2396% (384 样本)
angry: 74.7059% (170 样本)
excited: 78.9298% (299 样本)
frustrated: 68.5039% (381 样本)

每个类别的F1分数:
happy: 51.5152%
sad: 80.0866%
neutral: 67.0407%
angry: 67.5532%
excited: 80.4089%
frustrated: 67.0951%

总体指标:
总体准确率: 69.8090%
加权准确率 (W_ACC, 按测试集分布): 69.8090%
总体F1分数 (不加权宏平均): 68.9499%
手动计算宏平均F1: 68.9499%
加权F1 (W_F1, 按测试集分布): 70.1618%
============================================================
epoch: 24, train_loss: 17.8861, train_acc: 73.58, train_fscore: 73.3779, test_loss: 18.7225, test_acc: 69.809, test_fscore: 70.1618, time: 16.0 sec
epoch: 25, train_loss: 17.6225, train_acc: 73.7866, train_fscore: 73.5497, test_loss: 18.6545, test_acc: 68.3303, test_fscore: 69.227, time: 15.99 sec
epoch: 26, train_loss: 16.9723, train_acc: 74.6127, train_fscore: 74.3542, test_loss: 18.2046, test_acc: 68.8232, test_fscore: 69.4057, time: 15.8 sec

*** 新的最优F1分数: 71.0582% (Epoch 27) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 63.8889% (144 样本)
sad: 79.5918% (245 样本)
neutral: 63.5417% (384 样本)
angry: 74.1176% (170 样本)
excited: 76.5886% (299 样本)
frustrated: 68.2415% (381 样本)

每个类别的F1分数:
happy: 52.7221%
sad: 82.4524%
neutral: 69.6148%
angry: 68.2927%
excited: 78.6942%
frustrated: 67.3575%

总体指标:
总体准确率: 70.6100%
加权准确率 (W_ACC, 按测试集分布): 70.6100%
总体F1分数 (不加权宏平均): 69.8556%
手动计算宏平均F1: 69.8556%
加权F1 (W_F1, 按测试集分布): 71.0582%
============================================================
epoch: 27, train_loss: 16.4939, train_acc: 75.3528, train_fscore: 75.2665, test_loss: 17.4768, test_acc: 70.61, test_fscore: 71.0582, time: 15.82 sec
epoch: 28, train_loss: 15.4422, train_acc: 74.6299, train_fscore: 74.477, test_loss: 16.4855, test_acc: 68.7616, test_fscore: 69.473, time: 15.93 sec
epoch: 29, train_loss: 14.2783, train_acc: 76.0241, train_fscore: 75.8723, test_loss: 15.2416, test_acc: 68.7616, test_fscore: 69.3408, time: 15.82 sec
epoch: 30, train_loss: 12.6409, train_acc: 74.7504, train_fscore: 74.6831, test_loss: 13.6487, test_acc: 70.1171, test_fscore: 70.5839, time: 15.99 sec
----------best F-Score: 71.0582
              precision    recall  f1-score   support

           0     0.4802    0.6736    0.5607       144
           1     0.8889    0.6857    0.7742       245
           2     0.7645    0.6172    0.6830       384
           3     0.6318    0.7471    0.6846       170
           4     0.8321    0.7793    0.8048       299
           5     0.6259    0.7244    0.6715       381

    accuracy                         0.7012      1623
   macro avg     0.7039    0.7045    0.6965      1623
weighted avg     0.7241    0.7012    0.7058      1623

[[ 97   1   6   0  37   3]
 [  7 168  24   2   0  44]
 [ 43  11 237  17   4  72]
 [  0   0   1 127   0  42]
 [ 52   0  10   0 233   4]
 [  3   9  32  55   6 276]]
epoch: 31, train_loss: 11.0869, train_acc: 75.5077, train_fscore: 75.347, test_loss: 12.1896, test_acc: 69.2545, test_fscore: 69.8024, time: 15.98 sec

*** 新的最优F1分数: 71.2370% (Epoch 32) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 64.5833% (144 样本)
sad: 71.4286% (245 样本)
neutral: 65.3646% (384 样本)
angry: 68.2353% (170 样本)
excited: 78.5953% (299 样本)
frustrated: 73.2283% (381 样本)

每个类别的F1分数:
happy: 55.0296%
sad: 79.1855%
neutral: 69.0509%
angry: 66.0969%
excited: 81.0345%
frustrated: 69.0594%

总体指标:
总体准确率: 70.7948%
加权准确率 (W_ACC, 按测试集分布): 70.7948%
总体F1分数 (不加权宏平均): 69.9095%
手动计算宏平均F1: 69.9095%
加权F1 (W_F1, 按测试集分布): 71.2370%
============================================================
epoch: 32, train_loss: 9.7552, train_acc: 74.7676, train_fscore: 74.5763, test_loss: 11.2703, test_acc: 70.7948, test_fscore: 71.237, time: 17.23 sec
epoch: 33, train_loss: 9.1474, train_acc: 76.0929, train_fscore: 75.9227, test_loss: 10.9802, test_acc: 70.4251, test_fscore: 70.8983, time: 15.95 sec
epoch: 34, train_loss: 8.8161, train_acc: 76.0929, train_fscore: 75.895, test_loss: 11.0747, test_acc: 68.8232, test_fscore: 69.3703, time: 16.04 sec
epoch: 35, train_loss: 8.748, train_acc: 76.3855, train_fscore: 76.2698, test_loss: 10.0681, test_acc: 70.1171, test_fscore: 70.8112, time: 15.99 sec
epoch: 36, train_loss: 8.3204, train_acc: 76.9707, train_fscore: 76.8067, test_loss: 10.6202, test_acc: 69.3161, test_fscore: 69.8353, time: 16.0 sec

*** 新的最优F1分数: 71.5865% (Epoch 37) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.0556% (144 样本)
sad: 75.9184% (245 样本)
neutral: 67.4479% (384 样本)
angry: 70.5882% (170 样本)
excited: 77.5920% (299 样本)
frustrated: 67.9790% (381 样本)

每个类别的F1分数:
happy: 55.3672%
sad: 81.4004%
neutral: 70.3804%
angry: 67.9887%
excited: 80.0000%
frustrated: 67.6240%

总体指标:
总体准确率: 71.1029%
加权准确率 (W_ACC, 按测试集分布): 71.1029%
总体F1分数 (不加权宏平均): 70.4601%
手动计算宏平均F1: 70.4601%
加权F1 (W_F1, 按测试集分布): 71.5865%
============================================================
epoch: 37, train_loss: 8.3019, train_acc: 77.7281, train_fscore: 77.6564, test_loss: 10.11, test_acc: 71.1029, test_fscore: 71.5865, time: 16.0 sec
epoch: 38, train_loss: 8.1867, train_acc: 77.4527, train_fscore: 77.2796, test_loss: 11.4506, test_acc: 70.4868, test_fscore: 71.0049, time: 16.0 sec
epoch: 39, train_loss: 8.7746, train_acc: 77.3838, train_fscore: 77.2581, test_loss: 9.7556, test_acc: 70.1787, test_fscore: 70.6961, time: 15.98 sec

*** 新的最优F1分数: 71.6603% (Epoch 40) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 61.8056% (144 样本)
sad: 72.2449% (245 样本)
neutral: 67.9688% (384 样本)
angry: 70.0000% (170 样本)
excited: 84.6154% (299 样本)
frustrated: 68.5039% (381 样本)

每个类别的F1分数:
happy: 56.5079%
sad: 79.1946%
neutral: 70.1613%
angry: 68.0000%
excited: 82.2764%
frustrated: 67.3548%

总体指标:
总体准确率: 71.4726%
加权准确率 (W_ACC, 按测试集分布): 71.4726%
总体F1分数 (不加权宏平均): 70.5825%
手动计算宏平均F1: 70.5825%
加权F1 (W_F1, 按测试集分布): 71.6603%
============================================================
epoch: 40, train_loss: 8.3829, train_acc: 77.8313, train_fscore: 77.7968, test_loss: 15.137, test_acc: 71.4726, test_fscore: 71.6603, time: 16.0 sec
----------best F-Score: 71.6603
              precision    recall  f1-score   support

           0     0.5205    0.6181    0.5651       144
           1     0.8762    0.7224    0.7919       245
           2     0.7250    0.6797    0.7016       384
           3     0.6611    0.7000    0.6800       170
           4     0.8006    0.8462    0.8228       299
           5     0.6624    0.6850    0.6735       381

    accuracy                         0.7147      1623
   macro avg     0.7076    0.7086    0.7058      1623
weighted avg     0.7222    0.7147    0.7166      1623

[[ 89   2   5   0  47   1]
 [  6 177  27   2   0  33]
 [ 39  12 261  12  10  50]
 [  0   0   3 119   0  48]
 [ 33   0  12   0 253   1]
 [  4  11  52  47   6 261]]
epoch: 41, train_loss: 9.1991, train_acc: 77.0052, train_fscore: 76.7744, test_loss: 10.6373, test_acc: 69.8706, test_fscore: 70.4784, time: 15.99 sec

*** 新的最优F1分数: 71.7060% (Epoch 42) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 70.8333% (144 样本)
sad: 77.5510% (245 样本)
neutral: 62.2396% (384 样本)
angry: 74.7059% (170 样本)
excited: 78.9298% (299 样本)
frustrated: 69.0289% (381 样本)

每个类别的F1分数:
happy: 56.9832%
sad: 81.1966%
neutral: 69.2754%
angry: 67.9144%
excited: 80.9605%
frustrated: 68.0466%

总体指标:
总体准确率: 71.2877%
加权准确率 (W_ACC, 按测试集分布): 71.2877%
总体F1分数 (不加权宏平均): 70.7295%
手动计算宏平均F1: 70.7295%
加权F1 (W_F1, 按测试集分布): 71.7060%
============================================================
epoch: 42, train_loss: 9.3451, train_acc: 77.7281, train_fscore: 77.7279, test_loss: 15.2751, test_acc: 71.2877, test_fscore: 71.706, time: 16.0 sec
epoch: 43, train_loss: 9.5349, train_acc: 77.2806, train_fscore: 76.9825, test_loss: 12.0321, test_acc: 70.3019, test_fscore: 70.7768, time: 16.0 sec
epoch: 44, train_loss: 10.3178, train_acc: 78.9157, train_fscore: 78.9162, test_loss: 15.7823, test_acc: 70.4868, test_fscore: 71.0062, time: 15.99 sec
epoch: 45, train_loss: 10.1733, train_acc: 77.8141, train_fscore: 77.6304, test_loss: 14.3367, test_acc: 70.2403, test_fscore: 70.6304, time: 15.99 sec
epoch: 46, train_loss: 11.0081, train_acc: 78.537, train_fscore: 78.5391, test_loss: 14.5596, test_acc: 70.3019, test_fscore: 70.8053, time: 16.0 sec
epoch: 47, train_loss: 10.4879, train_acc: 77.7281, train_fscore: 77.4757, test_loss: 12.7196, test_acc: 70.4251, test_fscore: 70.8707, time: 15.98 sec
epoch: 48, train_loss: 10.1686, train_acc: 79.3632, train_fscore: 79.2912, test_loss: 15.0197, test_acc: 69.6858, test_fscore: 70.3645, time: 15.99 sec

*** 新的最优F1分数: 72.0045% (Epoch 49) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 62.5000% (144 样本)
sad: 75.5102% (245 样本)
neutral: 72.6562% (384 样本)
angry: 73.5294% (170 样本)
excited: 81.2709% (299 样本)
frustrated: 64.3045% (381 样本)

每个类别的F1分数:
happy: 59.4059%
sad: 79.7414%
neutral: 71.1735%
angry: 69.6379%
excited: 81.4070%
frustrated: 66.3058%

总体指标:
总体准确率: 71.9039%
加权准确率 (W_ACC, 按测试集分布): 71.9039%
总体F1分数 (不加权宏平均): 71.2786%
手动计算宏平均F1: 71.2786%
加权F1 (W_F1, 按测试集分布): 72.0045%
============================================================
epoch: 49, train_loss: 11.1643, train_acc: 77.9518, train_fscore: 77.7789, test_loss: 11.871, test_acc: 71.9039, test_fscore: 72.0045, time: 16.0 sec
epoch: 50, train_loss: 9.3131, train_acc: 79.3804, train_fscore: 79.2906, test_loss: 13.1623, test_acc: 70.7332, test_fscore: 71.2492, time: 15.99 sec
----------best F-Score: 72.0045
              precision    recall  f1-score   support

           0     0.5660    0.6250    0.5941       144
           1     0.8447    0.7551    0.7974       245
           2     0.6975    0.7266    0.7117       384
           3     0.6614    0.7353    0.6964       170
           4     0.8154    0.8127    0.8141       299
           5     0.6844    0.6430    0.6631       381

    accuracy                         0.7190      1623
   macro avg     0.7116    0.7163    0.7128      1623
weighted avg     0.7229    0.7190    0.7200      1623

[[ 90   2  10   0  41   1]
 [  3 185  24   2   0  31]
 [ 33  16 279   9   9  38]
 [  0   0   3 125   0  42]
 [ 32   1  22   0 243   1]
 [  1  15  62  53   5 245]]
epoch: 51, train_loss: 9.8135, train_acc: 79.2427, train_fscore: 79.0761, test_loss: 12.6289, test_acc: 70.6716, test_fscore: 71.1501, time: 15.99 sec
epoch: 52, train_loss: 9.2927, train_acc: 79.4492, train_fscore: 79.3971, test_loss: 10.9619, test_acc: 70.4251, test_fscore: 70.7791, time: 16.01 sec
epoch: 53, train_loss: 8.5955, train_acc: 79.6041, train_fscore: 79.4918, test_loss: 10.2446, test_acc: 70.1171, test_fscore: 70.5003, time: 15.99 sec
epoch: 54, train_loss: 8.1549, train_acc: 79.6213, train_fscore: 79.452, test_loss: 10.8578, test_acc: 71.2261, test_fscore: 71.6957, time: 15.99 sec
epoch: 55, train_loss: 8.1033, train_acc: 79.7762, train_fscore: 79.7265, test_loss: 10.3708, test_acc: 69.8706, test_fscore: 70.2446, time: 15.99 sec
epoch: 56, train_loss: 8.0787, train_acc: 79.7246, train_fscore: 79.6046, test_loss: 10.4625, test_acc: 70.6716, test_fscore: 71.1352, time: 15.79 sec
epoch: 57, train_loss: 7.8261, train_acc: 80.2065, train_fscore: 80.0541, test_loss: 9.6445, test_acc: 70.1171, test_fscore: 70.4906, time: 15.99 sec
epoch: 58, train_loss: 7.7066, train_acc: 80.1377, train_fscore: 80.0421, test_loss: 9.5494, test_acc: 71.2261, test_fscore: 71.6114, time: 15.64 sec
epoch: 59, train_loss: 7.5002, train_acc: 80.327, train_fscore: 80.2212, test_loss: 9.1632, test_acc: 69.809, test_fscore: 70.2722, time: 15.96 sec
epoch: 60, train_loss: 7.3212, train_acc: 80.9811, train_fscore: 80.8677, test_loss: 9.0512, test_acc: 71.1645, test_fscore: 71.5774, time: 15.99 sec
----------best F-Score: 72.0045
              precision    recall  f1-score   support

           0     0.4977    0.7431    0.5961       144
           1     0.8679    0.7510    0.8053       245
           2     0.6830    0.7240    0.7029       384
           3     0.6954    0.7118    0.7035       170
           4     0.8405    0.7224    0.7770       299
           5     0.6955    0.6535    0.6739       381

    accuracy                         0.7116      1623
   macro avg     0.7133    0.7176    0.7098      1623
weighted avg     0.7277    0.7116    0.7158      1623

[[107   1   5   0  30   1]
 [  4 184  28   2   1  26]
 [ 45  13 278   6   5  37]
 [  0   0   5 121   0  44]
 [ 57   0  25   0 216   1]
 [  2  14  66  45   5 249]]
epoch: 61, train_loss: 7.2012, train_acc: 80.568, train_fscore: 80.4553, test_loss: 9.1224, test_acc: 70.3019, test_fscore: 70.6922, time: 15.99 sec
epoch: 62, train_loss: 7.2343, train_acc: 80.9294, train_fscore: 80.821, test_loss: 9.1403, test_acc: 70.9181, test_fscore: 71.2567, time: 15.99 sec
epoch: 63, train_loss: 7.2547, train_acc: 80.6196, train_fscore: 80.5225, test_loss: 9.2121, test_acc: 70.8564, test_fscore: 71.2092, time: 15.99 sec
epoch: 64, train_loss: 7.125, train_acc: 80.9983, train_fscore: 80.8709, test_loss: 9.0555, test_acc: 70.9797, test_fscore: 71.3453, time: 16.01 sec
epoch: 65, train_loss: 7.076, train_acc: 81.5491, train_fscore: 81.4801, test_loss: 9.0922, test_acc: 69.9938, test_fscore: 70.258, time: 15.99 sec
epoch: 66, train_loss: 7.034, train_acc: 81.4114, train_fscore: 81.2806, test_loss: 9.0761, test_acc: 70.1171, test_fscore: 70.6007, time: 16.0 sec
epoch: 67, train_loss: 7.0426, train_acc: 81.3425, train_fscore: 81.2377, test_loss: 8.939, test_acc: 71.1645, test_fscore: 71.4366, time: 15.99 sec
epoch: 68, train_loss: 6.9586, train_acc: 81.9966, train_fscore: 81.8623, test_loss: 8.8362, test_acc: 71.5342, test_fscore: 71.9363, time: 15.99 sec
epoch: 69, train_loss: 6.9655, train_acc: 81.4974, train_fscore: 81.424, test_loss: 8.9974, test_acc: 70.8564, test_fscore: 71.2192, time: 15.99 sec

*** 新的最优F1分数: 72.0247% (Epoch 70) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 68.0556% (144 样本)
sad: 73.0612% (245 样本)
neutral: 72.6562% (384 样本)
angry: 71.1765% (170 样本)
excited: 76.9231% (299 样本)
frustrated: 67.1916% (381 样本)

每个类别的F1分数:
happy: 58.1602%
sad: 80.4494%
neutral: 71.6303%
angry: 69.7406%
excited: 79.3103%
frustrated: 67.5462%

总体指标:
总体准确率: 71.6574%
加权准确率 (W_ACC, 按测试集分布): 71.6574%
总体F1分数 (不加权宏平均): 71.1395%
手动计算宏平均F1: 71.1395%
加权F1 (W_F1, 按测试集分布): 72.0247%
============================================================
epoch: 70, train_loss: 6.9138, train_acc: 81.7556, train_fscore: 81.5693, test_loss: 8.7626, test_acc: 71.6574, test_fscore: 72.0247, time: 16.0 sec
----------best F-Score: 72.0247
              precision    recall  f1-score   support

           0     0.5078    0.6806    0.5816       144
           1     0.8950    0.7306    0.8045       245
           2     0.7063    0.7266    0.7163       384
           3     0.6836    0.7118    0.6974       170
           4     0.8185    0.7692    0.7931       299
           5     0.6790    0.6719    0.6755       381

    accuracy                         0.7166      1623
   macro avg     0.7150    0.7151    0.7114      1623
weighted avg     0.7291    0.7166    0.7202      1623

[[ 98   1   5   0  39   1]
 [  2 179  30   2   2  30]
 [ 42  11 279   6   4  42]
 [  0   0   2 121   0  47]
 [ 48   0  20   0 230   1]
 [  3   9  59  48   6 256]]
epoch: 71, train_loss: 6.9749, train_acc: 82.2031, train_fscore: 82.1306, test_loss: 9.0126, test_acc: 70.3635, test_fscore: 70.5166, time: 16.0 sec
epoch: 72, train_loss: 6.9611, train_acc: 82.4269, train_fscore: 82.257, test_loss: 9.1206, test_acc: 70.8564, test_fscore: 71.117, time: 16.14 sec
epoch: 73, train_loss: 7.0294, train_acc: 81.2392, train_fscore: 81.1103, test_loss: 9.2198, test_acc: 70.6716, test_fscore: 70.884, time: 15.84 sec
epoch: 74, train_loss: 6.9395, train_acc: 82.6334, train_fscore: 82.4972, test_loss: 8.9113, test_acc: 70.8564, test_fscore: 71.1052, time: 16.0 sec

*** 新的最优F1分数: 72.0534% (Epoch 75) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 66.6667% (144 样本)
sad: 73.8776% (245 样本)
neutral: 73.9583% (384 样本)
angry: 70.0000% (170 样本)
excited: 76.5886% (299 样本)
frustrated: 67.1916% (381 样本)

每个类别的F1分数:
happy: 59.6273%
sad: 80.0885%
neutral: 70.5590%
angry: 70.8333%
excited: 79.2388%
frustrated: 67.9947%

总体指标:
总体准确率: 71.7807%
加权准确率 (W_ACC, 按测试集分布): 71.7807%
总体F1分数 (不加权宏平均): 71.3903%
手动计算宏平均F1: 71.3903%
加权F1 (W_F1, 按测试集分布): 72.0534%
============================================================
epoch: 75, train_loss: 6.9035, train_acc: 82.117, train_fscore: 81.9719, test_loss: 8.9282, test_acc: 71.7807, test_fscore: 72.0534, time: 15.97 sec
epoch: 76, train_loss: 6.8745, train_acc: 82.685, train_fscore: 82.6558, test_loss: 8.8386, test_acc: 70.8564, test_fscore: 71.0808, time: 16.0 sec
epoch: 77, train_loss: 6.7851, train_acc: 82.1343, train_fscore: 81.9519, test_loss: 8.8918, test_acc: 71.4726, test_fscore: 71.7565, time: 16.01 sec
epoch: 78, train_loss: 6.8142, train_acc: 83.1497, train_fscore: 83.0819, test_loss: 8.7851, test_acc: 70.7948, test_fscore: 70.9654, time: 15.98 sec
epoch: 79, train_loss: 6.8653, train_acc: 82.8571, train_fscore: 82.7358, test_loss: 9.0519, test_acc: 71.1645, test_fscore: 71.4616, time: 16.02 sec
epoch: 80, train_loss: 6.8038, train_acc: 83.0293, train_fscore: 82.9011, test_loss: 8.7816, test_acc: 70.9797, test_fscore: 71.2135, time: 15.97 sec
----------best F-Score: 72.0534
              precision    recall  f1-score   support

           0     0.5393    0.6667    0.5963       144
           1     0.8744    0.7388    0.8009       245
           2     0.6746    0.7396    0.7056       384
           3     0.7169    0.7000    0.7083       170
           4     0.8208    0.7659    0.7924       299
           5     0.6882    0.6719    0.6799       381

    accuracy                         0.7178      1623
   macro avg     0.7190    0.7138    0.7139      1623
weighted avg     0.7273    0.7178    0.7205      1623

[[ 96   1   9   0  37   1]
 [  2 181  30   2   2  28]
 [ 37  12 284   5   7  39]
 [  0   1   3 119   0  47]
 [ 41   0  28   0 229   1]
 [  2  12  67  40   4 256]]
epoch: 81, train_loss: 6.7799, train_acc: 82.8744, train_fscore: 82.7821, test_loss: 8.7068, test_acc: 71.1029, test_fscore: 71.3473, time: 16.0 sec
epoch: 82, train_loss: 6.6991, train_acc: 82.9604, train_fscore: 82.8026, test_loss: 8.7912, test_acc: 71.5958, test_fscore: 71.8603, time: 15.99 sec
epoch: 83, train_loss: 6.6626, train_acc: 83.5628, train_fscore: 83.5014, test_loss: 8.6953, test_acc: 70.61, test_fscore: 70.7637, time: 15.81 sec
epoch: 84, train_loss: 6.7812, train_acc: 82.6678, train_fscore: 82.4898, test_loss: 8.8537, test_acc: 70.9181, test_fscore: 71.0432, time: 16.0 sec
epoch: 85, train_loss: 6.6561, train_acc: 83.494, train_fscore: 83.3961, test_loss: 8.6292, test_acc: 70.1171, test_fscore: 70.5668, time: 15.98 sec
epoch: 86, train_loss: 6.6796, train_acc: 83.2186, train_fscore: 83.1362, test_loss: 8.8697, test_acc: 70.9181, test_fscore: 70.9766, time: 15.99 sec
epoch: 87, train_loss: 6.7585, train_acc: 83.6145, train_fscore: 83.5157, test_loss: 8.6347, test_acc: 71.1029, test_fscore: 71.2513, time: 15.99 sec
epoch: 88, train_loss: 6.7081, train_acc: 83.3046, train_fscore: 83.1841, test_loss: 8.6362, test_acc: 71.9039, test_fscore: 71.9929, time: 16.03 sec
epoch: 89, train_loss: 6.6093, train_acc: 84.2857, train_fscore: 84.1578, test_loss: 8.7545, test_acc: 70.61, test_fscore: 70.9907, time: 15.97 sec
epoch: 90, train_loss: 6.6762, train_acc: 83.5112, train_fscore: 83.4151, test_loss: 8.6321, test_acc: 71.7807, test_fscore: 71.7069, time: 16.18 sec
----------best F-Score: 72.0534
              precision    recall  f1-score   support

           0     0.4641    0.7639    0.5774       144
           1     0.8507    0.7673    0.8069       245
           2     0.6867    0.7422    0.7134       384
           3     0.6578    0.7235    0.6891       170
           4     0.8547    0.6689    0.7505       299
           5     0.7052    0.6089    0.6535       381

    accuracy                         0.7012      1623
   macro avg     0.7032    0.7125    0.6985      1623
weighted avg     0.7240    0.7012    0.7057      1623

[[110   1   8   0  24   1]
 [  4 188  24   2   2  25]
 [ 45  14 285   6   3  31]
 [  0   3   5 123   0  39]
 [ 74   1  23   0 200   1]
 [  4  14  70  56   5 232]]
epoch: 91, train_loss: 6.6289, train_acc: 84.1136, train_fscore: 84.0138, test_loss: 8.8545, test_acc: 70.9181, test_fscore: 71.0965, time: 16.0 sec
epoch: 92, train_loss: 6.5331, train_acc: 84.0275, train_fscore: 83.9077, test_loss: 8.7043, test_acc: 70.9181, test_fscore: 71.0944, time: 16.0 sec
epoch: 93, train_loss: 6.5816, train_acc: 83.821, train_fscore: 83.6899, test_loss: 8.8914, test_acc: 70.7948, test_fscore: 70.8817, time: 15.99 sec

*** 新的最优F1分数: 72.3565% (Epoch 94) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 65.9722% (144 样本)
sad: 77.9592% (245 样本)
neutral: 71.8750% (384 样本)
angry: 71.7647% (170 样本)
excited: 80.2676% (299 样本)
frustrated: 65.0919% (381 样本)

每个类别的F1分数:
happy: 60.3175%
sad: 81.4499%
neutral: 71.9687%
angry: 68.9266%
excited: 80.4020%
frustrated: 66.6667%

总体指标:
总体准确率: 72.2120%
加权准确率 (W_ACC, 按测试集分布): 72.2120%
总体F1分数 (不加权宏平均): 71.6219%
手动计算宏平均F1: 71.6219%
加权F1 (W_F1, 按测试集分布): 72.3565%
============================================================
epoch: 94, train_loss: 6.5293, train_acc: 84.3373, train_fscore: 84.263, test_loss: 8.7256, test_acc: 72.212, test_fscore: 72.3565, time: 16.0 sec
epoch: 95, train_loss: 6.6119, train_acc: 84.4578, train_fscore: 84.3612, test_loss: 8.6307, test_acc: 71.0413, test_fscore: 71.1078, time: 15.98 sec
epoch: 96, train_loss: 6.6617, train_acc: 84.957, train_fscore: 84.8373, test_loss: 8.5139, test_acc: 71.7807, test_fscore: 71.9459, time: 16.0 sec
epoch: 97, train_loss: 6.4578, train_acc: 85.0947, train_fscore: 85.0388, test_loss: 8.7357, test_acc: 71.1645, test_fscore: 71.2776, time: 16.0 sec
epoch: 98, train_loss: 6.5378, train_acc: 84.8365, train_fscore: 84.6797, test_loss: 8.5424, test_acc: 71.1645, test_fscore: 71.3174, time: 16.0 sec
epoch: 99, train_loss: 6.5526, train_acc: 84.8365, train_fscore: 84.7724, test_loss: 8.6416, test_acc: 71.7807, test_fscore: 71.7714, time: 15.99 sec
epoch: 100, train_loss: 6.5854, train_acc: 85.3012, train_fscore: 85.1987, test_loss: 8.9053, test_acc: 71.6574, test_fscore: 71.7581, time: 16.0 sec
----------best F-Score: 72.3565
              precision    recall  f1-score   support

           0     0.5640    0.6736    0.6139       144
           1     0.8612    0.7347    0.7930       245
           2     0.6962    0.7578    0.7257       384
           3     0.6440    0.7235    0.6814       170
           4     0.8175    0.7793    0.7979       299
           5     0.6925    0.6325    0.6612       381

    accuracy                         0.7178      1623
   macro avg     0.7126    0.7169    0.7122      1623
weighted avg     0.7254    0.7178    0.7195      1623

[[ 97   1   8   0  37   1]
 [  2 180  31   2   3  27]
 [ 32  14 291   8   6  33]
 [  0   1   1 123   0  45]
 [ 40   1  24   0 233   1]
 [  1  12  63  58   6 241]]
epoch: 101, train_loss: 6.562, train_acc: 84.8021, train_fscore: 84.6694, test_loss: 8.7165, test_acc: 71.7807, test_fscore: 71.8586, time: 16.0 sec
epoch: 102, train_loss: 6.5535, train_acc: 85.4389, train_fscore: 85.3515, test_loss: 8.8853, test_acc: 71.1029, test_fscore: 71.2561, time: 16.0 sec
epoch: 103, train_loss: 6.5932, train_acc: 84.9225, train_fscore: 84.8352, test_loss: 9.1975, test_acc: 71.9655, test_fscore: 72.0003, time: 15.98 sec
epoch: 104, train_loss: 6.5757, train_acc: 85.4217, train_fscore: 85.2912, test_loss: 9.0928, test_acc: 71.5342, test_fscore: 71.7002, time: 16.0 sec
epoch: 105, train_loss: 6.459, train_acc: 86.2134, train_fscore: 86.1279, test_loss: 9.0027, test_acc: 71.1645, test_fscore: 71.1894, time: 15.99 sec
epoch: 106, train_loss: 6.5558, train_acc: 85.4045, train_fscore: 85.2936, test_loss: 9.0895, test_acc: 70.9181, test_fscore: 70.9453, time: 16.02 sec
epoch: 107, train_loss: 6.5657, train_acc: 85.6627, train_fscore: 85.5901, test_loss: 9.0993, test_acc: 70.8564, test_fscore: 70.8789, time: 15.98 sec
epoch: 108, train_loss: 6.4185, train_acc: 85.7143, train_fscore: 85.6202, test_loss: 9.1469, test_acc: 72.0271, test_fscore: 72.1356, time: 15.98 sec
epoch: 109, train_loss: 6.4945, train_acc: 86.0413, train_fscore: 85.9642, test_loss: 8.9682, test_acc: 71.7807, test_fscore: 71.8246, time: 16.0 sec

*** 新的最优F1分数: 72.5297% (Epoch 110) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 65.2778% (144 样本)
sad: 74.2857% (245 样本)
neutral: 78.6458% (384 样本)
angry: 70.5882% (170 样本)
excited: 76.2542% (299 样本)
frustrated: 65.3543% (381 样本)

每个类别的F1分数:
happy: 61.6393%
sad: 79.4760%
neutral: 73.7485%
angry: 68.9655%
excited: 79.4425%
frustrated: 67.1159%

总体指标:
总体准确率: 72.3968%
加权准确率 (W_ACC, 按测试集分布): 72.3968%
总体F1分数 (不加权宏平均): 71.7313%
手动计算宏平均F1: 71.7313%
加权F1 (W_F1, 按测试集分布): 72.5297%
============================================================
epoch: 110, train_loss: 6.5276, train_acc: 86.0413, train_fscore: 85.9283, test_loss: 8.8779, test_acc: 72.3968, test_fscore: 72.5297, time: 15.99 sec
----------best F-Score: 72.5297
              precision    recall  f1-score   support

           0     0.5839    0.6528    0.6164       144
           1     0.8545    0.7429    0.7948       245
           2     0.6943    0.7865    0.7375       384
           3     0.6742    0.7059    0.6897       170
           4     0.8291    0.7625    0.7944       299
           5     0.6898    0.6535    0.6712       381

    accuracy                         0.7240      1623
   macro avg     0.7209    0.7173    0.7173      1623
weighted avg     0.7303    0.7240    0.7253      1623

[[ 94   2  11   0  36   1]
 [  2 182  27   2   2  30]
 [ 23  16 302   6   4  33]
 [  0   1   3 120   0  46]
 [ 41   1  27   0 228   2]
 [  1  11  65  50   5 249]]
epoch: 111, train_loss: 6.4913, train_acc: 86.3855, train_fscore: 86.3103, test_loss: 9.0911, test_acc: 70.2403, test_fscore: 70.2629, time: 15.97 sec
epoch: 112, train_loss: 6.3639, train_acc: 86.179, train_fscore: 86.0594, test_loss: 8.8115, test_acc: 72.4584, test_fscore: 72.5252, time: 15.83 sec
epoch: 113, train_loss: 6.4553, train_acc: 86.4544, train_fscore: 86.3802, test_loss: 9.0688, test_acc: 71.2877, test_fscore: 71.3877, time: 15.77 sec
epoch: 114, train_loss: 6.5105, train_acc: 86.3339, train_fscore: 86.2119, test_loss: 8.8541, test_acc: 71.9039, test_fscore: 71.9721, time: 15.82 sec
epoch: 115, train_loss: 6.3937, train_acc: 86.8158, train_fscore: 86.7349, test_loss: 8.8709, test_acc: 71.0413, test_fscore: 71.1203, time: 15.99 sec
epoch: 116, train_loss: 6.4408, train_acc: 86.1962, train_fscore: 86.0945, test_loss: 8.8152, test_acc: 71.9039, test_fscore: 71.9432, time: 15.99 sec
epoch: 117, train_loss: 6.476, train_acc: 87.0224, train_fscore: 86.943, test_loss: 8.906, test_acc: 70.3019, test_fscore: 70.4272, time: 15.99 sec
epoch: 118, train_loss: 6.3267, train_acc: 86.4372, train_fscore: 86.345, test_loss: 8.9, test_acc: 71.7807, test_fscore: 71.9317, time: 16.01 sec
epoch: 119, train_loss: 6.2976, train_acc: 87.1256, train_fscore: 87.0392, test_loss: 9.0782, test_acc: 70.9797, test_fscore: 71.136, time: 15.98 sec

*** 新的最优F1分数: 72.5468% (Epoch 120) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 65.2778% (144 样本)
sad: 74.2857% (245 样本)
neutral: 80.2083% (384 样本)
angry: 72.3529% (170 样本)
excited: 77.5920% (299 样本)
frustrated: 62.2047% (381 样本)

每个类别的F1分数:
happy: 61.2378%
sad: 80.0000%
neutral: 72.8994%
angry: 70.6897%
excited: 79.8623%
frustrated: 66.7606%

总体指标:
总体准确率: 72.4584%
加权准确率 (W_ACC, 按测试集分布): 72.4584%
总体F1分数 (不加权宏平均): 71.9083%
手动计算宏平均F1: 71.9083%
加权F1 (W_F1, 按测试集分布): 72.5468%
============================================================
epoch: 120, train_loss: 6.2925, train_acc: 87.1945, train_fscore: 87.1098, test_loss: 8.807, test_acc: 72.4584, test_fscore: 72.5468, time: 16.0 sec
----------best F-Score: 72.5468
              precision    recall  f1-score   support

           0     0.5767    0.6528    0.6124       144
           1     0.8667    0.7429    0.8000       245
           2     0.6681    0.8021    0.7290       384
           3     0.6910    0.7235    0.7069       170
           4     0.8227    0.7759    0.7986       299
           5     0.7204    0.6220    0.6676       381

    accuracy                         0.7246      1623
   macro avg     0.7243    0.7199    0.7191      1623
weighted avg     0.7331    0.7246    0.7255      1623

[[ 94   1  10   0  38   1]
 [  2 182  30   2   2  27]
 [ 30  12 308   5   6  23]
 [  0   3   4 123   0  40]
 [ 35   1  30   0 232   1]
 [  2  11  79  48   4 237]]
epoch: 121, train_loss: 6.3843, train_acc: 86.8847, train_fscore: 86.8095, test_loss: 8.886, test_acc: 72.212, test_fscore: 72.1594, time: 15.99 sec
epoch: 122, train_loss: 6.2737, train_acc: 86.5404, train_fscore: 86.4177, test_loss: 9.0206, test_acc: 72.1503, test_fscore: 72.1833, time: 16.0 sec
epoch: 123, train_loss: 6.3551, train_acc: 87.2633, train_fscore: 87.1728, test_loss: 8.7576, test_acc: 71.9655, test_fscore: 72.0912, time: 15.99 sec
epoch: 124, train_loss: 6.3417, train_acc: 87.5731, train_fscore: 87.4892, test_loss: 9.0881, test_acc: 72.4584, test_fscore: 72.4052, time: 15.99 sec
epoch: 125, train_loss: 6.3665, train_acc: 87.3494, train_fscore: 87.2504, test_loss: 8.972, test_acc: 71.6574, test_fscore: 71.6553, time: 16.0 sec
epoch: 126, train_loss: 6.2293, train_acc: 87.5387, train_fscore: 87.4506, test_loss: 9.0242, test_acc: 71.7807, test_fscore: 71.9257, time: 16.01 sec

*** 新的最优F1分数: 72.6937% (Epoch 127) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 56.2500% (144 样本)
sad: 72.6531% (245 样本)
neutral: 82.0312% (384 样本)
angry: 70.5882% (170 样本)
excited: 80.6020% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 57.8571%
sad: 78.5872%
neutral: 73.8570%
angry: 71.2166%
excited: 79.6694%
frustrated: 68.5237%

总体指标:
总体准确率: 72.7665%
加权准确率 (W_ACC, 按测试集分布): 72.7665%
总体F1分数 (不加权宏平均): 71.6185%
手动计算宏平均F1: 71.6185%
加权F1 (W_F1, 按测试集分布): 72.6937%
============================================================
epoch: 127, train_loss: 6.2952, train_acc: 87.7108, train_fscore: 87.6253, test_loss: 9.2784, test_acc: 72.7665, test_fscore: 72.6937, time: 15.99 sec
epoch: 128, train_loss: 6.3567, train_acc: 87.2633, train_fscore: 87.1813, test_loss: 8.9946, test_acc: 71.2877, test_fscore: 71.2875, time: 16.0 sec
epoch: 129, train_loss: 6.285, train_acc: 87.4355, train_fscore: 87.3184, test_loss: 9.2945, test_acc: 72.1503, test_fscore: 72.3393, time: 15.98 sec

*** 新的最优F1分数: 72.9440% (Epoch 130) ***

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.1667% (144 样本)
sad: 73.8776% (245 样本)
neutral: 80.7292% (384 样本)
angry: 73.5294% (170 样本)
excited: 82.2742% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 57.5646%
sad: 79.5604%
neutral: 74.6089%
angry: 71.0227%
excited: 80.0000%
frustrated: 68.1440%

总体指标:
总体准确率: 73.0746%
加权准确率 (W_ACC, 按测试集分布): 73.0746%
总体F1分数 (不加权宏平均): 71.8168%
手动计算宏平均F1: 71.8168%
加权F1 (W_F1, 按测试集分布): 72.9440%
============================================================
epoch: 130, train_loss: 6.29, train_acc: 87.5215, train_fscore: 87.4484, test_loss: 9.3502, test_acc: 73.0746, test_fscore: 72.944, time: 16.0 sec
----------best F-Score: 72.944
              precision    recall  f1-score   support

           0     0.6142    0.5417    0.5756       144
           1     0.8619    0.7388    0.7956       245
           2     0.6935    0.8073    0.7461       384
           3     0.6868    0.7353    0.7102       170
           4     0.7785    0.8227    0.8000       299
           5     0.7214    0.6457    0.6814       381

    accuracy                         0.7307      1623
   macro avg     0.7260    0.7152    0.7182      1623
weighted avg     0.7334    0.7307    0.7294      1623

[[ 78   1   8   0  56   1]
 [  2 181  29   2   2  29]
 [ 23  14 310   5   7  25]
 [  0   3   3 125   0  39]
 [ 23   1  28   0 246   1]
 [  1  10  69  50   5 246]]
epoch: 131, train_loss: 6.344, train_acc: 87.4527, train_fscore: 87.3728, test_loss: 9.0399, test_acc: 71.7807, test_fscore: 71.877, time: 15.8 sec
epoch: 132, train_loss: 6.3326, train_acc: 88.0551, train_fscore: 87.9633, test_loss: 9.2743, test_acc: 72.212, test_fscore: 72.2136, time: 15.98 sec
epoch: 133, train_loss: 6.2989, train_acc: 88.21, train_fscore: 88.1414, test_loss: 9.4795, test_acc: 71.0413, test_fscore: 71.0855, time: 16.0 sec
epoch: 134, train_loss: 6.228, train_acc: 88.4509, train_fscore: 88.3437, test_loss: 9.1612, test_acc: 71.6574, test_fscore: 71.7007, time: 16.03 sec
epoch: 135, train_loss: 6.2636, train_acc: 88.3993, train_fscore: 88.3191, test_loss: 8.999, test_acc: 72.3968, test_fscore: 72.1621, time: 15.97 sec
epoch: 136, train_loss: 6.1992, train_acc: 88.4165, train_fscore: 88.3278, test_loss: 9.1565, test_acc: 71.4726, test_fscore: 71.5289, time: 15.99 sec
epoch: 137, train_loss: 6.1848, train_acc: 88.9501, train_fscore: 88.8789, test_loss: 9.0156, test_acc: 71.5958, test_fscore: 71.5383, time: 16.0 sec
epoch: 138, train_loss: 6.2299, train_acc: 88.4682, train_fscore: 88.3796, test_loss: 9.2119, test_acc: 72.1503, test_fscore: 72.0933, time: 15.99 sec
epoch: 139, train_loss: 6.1565, train_acc: 88.5886, train_fscore: 88.4962, test_loss: 9.0767, test_acc: 72.3968, test_fscore: 72.4644, time: 15.97 sec
epoch: 140, train_loss: 6.081, train_acc: 89.2083, train_fscore: 89.1459, test_loss: 9.307, test_acc: 71.9039, test_fscore: 71.6825, time: 15.62 sec
----------best F-Score: 72.944
              precision    recall  f1-score   support

           0     0.6142    0.5417    0.5756       144
           1     0.8619    0.7388    0.7956       245
           2     0.6935    0.8073    0.7461       384
           3     0.6868    0.7353    0.7102       170
           4     0.7785    0.8227    0.8000       299
           5     0.7214    0.6457    0.6814       381

    accuracy                         0.7307      1623
   macro avg     0.7260    0.7152    0.7182      1623
weighted avg     0.7334    0.7307    0.7294      1623

[[ 78   1   8   0  56   1]
 [  2 181  29   2   2  29]
 [ 23  14 310   5   7  25]
 [  0   3   3 125   0  39]
 [ 23   1  28   0 246   1]
 [  1  10  69  50   5 246]]
epoch: 141, train_loss: 6.1915, train_acc: 88.5886, train_fscore: 88.5087, test_loss: 9.3032, test_acc: 71.9039, test_fscore: 71.793, time: 15.98 sec
epoch: 142, train_loss: 6.2286, train_acc: 88.7263, train_fscore: 88.6388, test_loss: 9.1953, test_acc: 72.7665, test_fscore: 72.8141, time: 15.99 sec
epoch: 143, train_loss: 6.1497, train_acc: 88.7435, train_fscore: 88.6536, test_loss: 9.2855, test_acc: 72.52, test_fscore: 72.5442, time: 16.01 sec
epoch: 144, train_loss: 6.1313, train_acc: 89.1566, train_fscore: 89.0894, test_loss: 9.3784, test_acc: 72.212, test_fscore: 72.1595, time: 15.99 sec
epoch: 145, train_loss: 6.137, train_acc: 89.4664, train_fscore: 89.3625, test_loss: 9.2338, test_acc: 72.1503, test_fscore: 72.2655, time: 15.8 sec
epoch: 146, train_loss: 6.0464, train_acc: 89.6558, train_fscore: 89.6058, test_loss: 9.3228, test_acc: 71.8423, test_fscore: 71.667, time: 15.8 sec
epoch: 147, train_loss: 6.135, train_acc: 89.2255, train_fscore: 89.1316, test_loss: 9.4151, test_acc: 71.9039, test_fscore: 71.9336, time: 16.02 sec
epoch: 148, train_loss: 6.0965, train_acc: 89.4148, train_fscore: 89.3225, test_loss: 9.1282, test_acc: 72.0271, test_fscore: 71.9208, time: 15.98 sec
epoch: 149, train_loss: 6.1176, train_acc: 89.2255, train_fscore: 89.1551, test_loss: 9.4215, test_acc: 71.2261, test_fscore: 71.2019, time: 15.98 sec
epoch: 150, train_loss: 6.165, train_acc: 89.6041, train_fscore: 89.5241, test_loss: 9.3796, test_acc: 72.3968, test_fscore: 72.4076, time: 16.0 sec
----------best F-Score: 72.944
              precision    recall  f1-score   support

           0     0.6142    0.5417    0.5756       144
           1     0.8619    0.7388    0.7956       245
           2     0.6935    0.8073    0.7461       384
           3     0.6868    0.7353    0.7102       170
           4     0.7785    0.8227    0.8000       299
           5     0.7214    0.6457    0.6814       381

    accuracy                         0.7307      1623
   macro avg     0.7260    0.7152    0.7182      1623
weighted avg     0.7334    0.7307    0.7294      1623

[[ 78   1   8   0  56   1]
 [  2 181  29   2   2  29]
 [ 23  14 310   5   7  25]
 [  0   3   3 125   0  39]
 [ 23   1  28   0 246   1]
 [  1  10  69  50   5 246]]
Test performance..
Best F-Score: 72.944
              precision    recall  f1-score   support

           0     0.6142    0.5417    0.5756       144
           1     0.8619    0.7388    0.7956       245
           2     0.6935    0.8073    0.7461       384
           3     0.6868    0.7353    0.7102       170
           4     0.7785    0.8227    0.8000       299
           5     0.7214    0.6457    0.6814       381

    accuracy                         0.7307      1623
   macro avg     0.7260    0.7152    0.7182      1623
weighted avg     0.7334    0.7307    0.7294      1623

[[ 78   1   8   0  56   1]
 [  2 181  29   2   2  29]
 [ 23  14 310   5   7  25]
 [  0   3   3 125   0  39]
 [ 23   1  28   0 246   1]
 [  1  10  69  50   5 246]]

==================================================
DETAILED EVALUATION WITH WEIGHTS
==================================================

============================================================
测试集详细评估结果
============================================================

每个类别的准确率:
happy: 54.1667% (144 样本)
sad: 73.8776% (245 样本)
neutral: 80.7292% (384 样本)
angry: 73.5294% (170 样本)
excited: 82.2742% (299 样本)
frustrated: 64.5669% (381 样本)

每个类别的F1分数:
happy: 57.5646%
sad: 79.5604%
neutral: 74.6089%
angry: 71.0227%
excited: 80.0000%
frustrated: 68.1440%

总体指标:
总体准确率: 73.0746%
加权准确率 (W_ACC, 按测试集分布): 73.0746%
总体F1分数 (不加权宏平均): 71.8168%
手动计算宏平均F1: 71.8168%
加权F1 (W_F1, 按测试集分布): 72.9440%
============================================================

进程已结束，退出代码为 0
