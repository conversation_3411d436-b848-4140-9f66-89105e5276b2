#!/usr/bin/env python3
"""
简单的3分类任务：KLDivLoss 和 NLLLoss 测试demo
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

def test_kl_div_loss():
    """
    KL散度损失函数测试 - 3分类任务

    输入维度：
    - input: (batch_size, num_classes) - log概率，用log_softmax
    - target: (batch_size, num_classes) - 概率分布，用softmax，和为1
    """
    print("="*50)
    print("KL散度损失函数 (KLDivLoss) - 3分类测试")
    print("="*50)

    # 3分类任务
    batch_size = 2
    num_classes = 3

    # 模型预测的logits
    pred_logits = torch.tensor([
        [2.0, 1.0, 0.5],  # 样本1: 预测倾向于类别0
        [0.8, 2.5, 1.2]   # 样本2: 预测倾向于类别1
    ], dtype=torch.float32)

    # 目标分布 (可能来自另一个模型或软标签)
    target_logits = torch.tensor([
        [1.8, 1.2, 0.6],  # 样本1的目标分布
        [1.0, 2.8, 1.5]   # 样本2的目标分布
    ], dtype=torch.float32)

    print(f"输入维度: {pred_logits.shape} = (batch_size={batch_size}, num_classes={num_classes})")
    print(f"预测logits:\n{pred_logits}")
    print(f"目标logits:\n{target_logits}")

    # KLDivLoss的正确用法
    kl_criterion = nn.KLDivLoss(reduction='batchmean')

    # input: log概率
    pred_log_probs = F.log_softmax(pred_logits, dim=1)
    # target: 概率分布
    target_probs = F.softmax(target_logits, dim=1)

    print(f"\n✅ 正确格式:")
    print(f"input (log概率): {pred_log_probs.shape}")
    print(pred_log_probs)
    print(f"target (概率): {target_probs.shape}")
    print(target_probs)
    print(f"target概率和: {target_probs.sum(dim=1)}")  # 应该是[1.0, 1.0]

    # 计算KL散度损失
    kl_loss = kl_criterion(pred_log_probs, target_probs)
    print(f"\nKL散度损失: {kl_loss.item():.6f}")

    # 错误用法示例
    print(f"\n❌ 错误用法:")
    # 错误1: input用softmax而不是log_softmax
    wrong_input = F.softmax(pred_logits, dim=1)
    wrong_loss1 = kl_criterion(wrong_input, target_probs)
    print(f"错误1 - input用softmax: {wrong_loss1.item():.6f} (可能为负!)")

    # 错误2: target用log_softmax而不是softmax
    wrong_target = F.log_softmax(target_logits, dim=1)
    wrong_loss2 = kl_criterion(pred_log_probs, wrong_target)
    print(f"错误2 - target用log_softmax: {wrong_loss2.item():.6f}")

    return kl_loss

def test_nll_loss():
    """
    NLL损失函数测试 - 3分类任务

    输入维度：
    - input: (batch_size, num_classes) - log概率，用log_softmax
    - target: (batch_size,) - 类别索引，整数标签
    """
    print("\n" + "="*50)
    print("负对数似然损失函数 (NLLLoss) - 3分类测试")
    print("="*50)

    # 3分类任务
    batch_size = 2
    num_classes = 3

    # 模型预测的logits
    logits = torch.tensor([
        [2.0, 1.0, 0.5],  # 样本1: 预测倾向于类别0
        [0.8, 2.5, 1.2]   # 样本2: 预测倾向于类别1
    ], dtype=torch.float32)

    # 真实标签 (类别索引)
    targets = torch.tensor([0, 1], dtype=torch.long)

    print(f"输入维度: {logits.shape} = (batch_size={batch_size}, num_classes={num_classes})")
    print(f"目标维度: {targets.shape} = (batch_size={batch_size},)")
    print(f"预测logits:\n{logits}")
    print(f"真实标签: {targets}")

    # NLLLoss的正确用法
    nll_criterion = nn.NLLLoss()

    # input: log概率
    log_probs = F.log_softmax(logits, dim=1)
    # target: 类别索引

    print(f"\n✅ 正确格式:")
    print(f"input (log概率): {log_probs.shape}")
    print(log_probs)
    print(f"target (类别索引): {targets.shape}")
    print(targets)

    # 计算NLL损失
    nll_loss = nll_criterion(log_probs, targets)
    print(f"\nNLL损失: {nll_loss.item():.6f}")

    # 手动验证
    print(f"\n手动验证:")
    print(f"样本1: log_prob[{targets[0]}] = {log_probs[0, targets[0]].item():.6f}")
    print(f"样本2: log_prob[{targets[1]}] = {log_probs[1, targets[1]].item():.6f}")
    manual_nll = -log_probs[range(len(targets)), targets].mean()
    print(f"手动计算: {manual_nll.item():.6f}")

    # 与CrossEntropyLoss对比
    ce_criterion = nn.CrossEntropyLoss()
    ce_loss = ce_criterion(logits, targets)  # CrossEntropy直接用logits
    print(f"CrossEntropyLoss: {ce_loss.item():.6f}")
    print("NLL + log_softmax = CrossEntropy ✓")

    # ❌ 错误用法：直接用原始logits而不做log_softmax
    print(f"\n❌ 错误用法测试:")
    print("直接用原始logits (不做log_softmax):")
    wrong_nll_loss = nll_criterion(logits, targets)  # 直接用logits
    print(f"错误的NLL损失: {wrong_nll_loss.item():.6f}")
    print("解释: 这个结果是错误的！NLL期望输入log概率，不是原始logits")
    print("原始logits可能很大，导致损失计算完全错误")

    # 显示差异
    print(f"\n正确NLL损失: {nll_loss.item():.6f}")
    print(f"错误NLL损失: {wrong_nll_loss.item():.6f}")
    print(f"差异: {abs(nll_loss.item() - wrong_nll_loss.item()):.6f}")

    return nll_loss

def main():
    print("简单3分类任务 - 损失函数测试")
    print("重要提示：")
    print("- KLDivLoss: input用log_softmax, target用softmax")
    print("- NLLLoss: input用log_softmax, target用类别索引")

    # 测试KL散度损失
    test_kl_div_loss()

    # 测试NLL损失
    test_nll_loss()

    print("\n" + "="*50)
    print("总结:")
    print("1. KLDivLoss 用于分布匹配，输入是两个分布")
    print("2. NLLLoss 用于分类任务，输入是概率和标签")
    print("3. 输入格式错误会导致负值或异常结果")
    print("4. KL散度理论上>=0，负值说明格式错误")
    print("="*50)

if __name__ == "__main__":
    main()
