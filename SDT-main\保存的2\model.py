import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class MaskedKLDivLoss(nn.Module):
    def __init__(self):
        super(MaskedKLDivLoss, self).__init__()
        self.loss = nn.KLDivLoss(reduction='sum')

    def forward(self, log_pred, target, mask):
        mask_ = mask.view(-1, 1)
        loss = self.loss(log_pred * mask_, target * mask_) / torch.sum(mask)
        return loss


class MaskedNLLLoss(nn.Module):
    def __init__(self, weight=None):
        super(MaskedNLLLoss, self).__init__()
        self.weight = weight
        self.loss = nn.NLLLoss(weight=weight, reduction='sum')

    def forward(self, pred, target, mask):
        mask_ = mask.view(-1, 1)
        if type(self.weight) == type(None):
            loss = self.loss(pred * mask_, target) / torch.sum(mask)
        else:
            loss = self.loss(pred * mask_, target) \
                   / torch.sum(self.weight[target] * mask_.squeeze())
        return loss


def gelu(x):
     return 0.5 * x * (1 + torch.tanh(math.sqrt(2 / math.pi) * (x + 0.044715 * torch.pow(x, 3))))
# def gelu(x):   #修改1
#     return F.gelu(x, approximate='tanh')

class PassRateGenerator(nn.Module):
    """
    通过率生成器 - 支持掩码
    输入: 三维矩阵 (batch_size, seq_len, hidden_dim) + 掩码
    输出: 一维通过率 (batch_size,) 范围[0,1]
    """
    def __init__(self, hidden_dim, dropout=0.3):
        super(PassRateGenerator, self).__init__()
        
        # 通过率生成网络
        self.pass_rate_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()  # 确保输出在[0,1]范围
        )
        
    def forward(self, x, mask=None):
        """
        Args:
            x: (batch_size, seq_len, hidden_dim) 输入特征矩阵
            mask: (batch_size, seq_len) 掩码，1表示有效位置，0表示padding
        
        Returns:
            pass_rate: (batch_size,) 每个样本的通过率
        """
        batch_size, seq_len, hidden_dim = x.shape
        
        if mask is not None:
            # 将掩码扩展到特征维度
            mask_expanded = mask.unsqueeze(-1).expand_as(x)  # (batch_size, seq_len, hidden_dim)
            
            # 将padding位置的特征置零
            x_masked = x * mask_expanded
            
            # 计算每个样本的有效长度
            valid_lengths = mask.sum(dim=1, keepdim=True).float()  # (batch_size, 1)
            valid_lengths = torch.clamp(valid_lengths, min=1.0)  # 避免除零
            
            # 对有效位置求平均 (masked average pooling)
            pooled_features = x_masked.sum(dim=1) / valid_lengths  # (batch_size, hidden_dim)
        else:
            # 没有掩码时，直接平均池化
            pooled_features = x.mean(dim=1)  # (batch_size, hidden_dim)
        
        # 生成通过率
        pass_rate = self.pass_rate_net(pooled_features)  # (batch_size, 1)
        pass_rate = pass_rate.squeeze(-1)  # (batch_size,)
        
        return pass_rate


class AttentionPassRateGenerator(nn.Module):
    """
    基于注意力的通过率生成器 - 更高级版本
    """
    def __init__(self, hidden_dim, dropout=0.3):
        super(AttentionPassRateGenerator, self).__init__()
        
        # 注意力层
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh(),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 通过率生成网络
        self.pass_rate_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, mask=None):
        """
        Args:
            x: (batch_size, seq_len, hidden_dim)
            mask: (batch_size, seq_len)
        
        Returns:
            pass_rate: (batch_size,)
        """
        batch_size, seq_len, hidden_dim = x.shape
        
        # 计算注意力权重
        attn_scores = self.attention(x)  # (batch_size, seq_len, 1)
        attn_scores = attn_scores.squeeze(-1)  # (batch_size, seq_len)
        
        if mask is not None:
            # 将padding位置的注意力分数设为很小的负数
            attn_scores = attn_scores.masked_fill(mask.eq(0), -1e10)
        
        # Softmax归一化
        attn_weights = F.softmax(attn_scores, dim=1)  # (batch_size, seq_len)
        
        # 加权平均池化
        attn_weights_expanded = attn_weights.unsqueeze(-1)  # (batch_size, seq_len, 1)
        pooled_features = (x * attn_weights_expanded).sum(dim=1)  # (batch_size, hidden_dim)
        
        # 生成通过率
        pass_rate = self.pass_rate_net(pooled_features)  # (batch_size, 1)
        pass_rate = pass_rate.squeeze(-1)  # (batch_size,)
        
        return pass_rate

class PositionwiseFeedForward(nn.Module):
    def __init__(self, d_model, d_ff, dropout=0.1):
        super(PositionwiseFeedForward, self).__init__()
        self.w_1 = nn.Linear(d_model, d_ff)
        self.w_2 = nn.Linear(d_ff, d_model)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        self.actv = gelu
        self.dropout_1 = nn.Dropout(dropout)
        self.dropout_2 = nn.Dropout(dropout)

        # ----------------- 修改2：改进初始化 -----------------
        # nn.init.kaiming_normal_(self.w_1.weight, nonlinearity='gelu')  # Kaiming初始化
        nn.init.xavier_normal_(self.w_2.weight)

    def forward(self, x):
        inter = self.dropout_1(self.actv(self.w_1(self.layer_norm(x))))
        output = self.dropout_2(self.w_2(inter))
        return output + x


class MultiHeadedAttention(nn.Module):
    def __init__(self, head_count, model_dim, dropout=0.1):
        assert model_dim % head_count == 0
        self.dim_per_head = model_dim // head_count
        self.model_dim = model_dim

        super(MultiHeadedAttention, self).__init__()
        self.head_count = head_count

        self.linear_k = nn.Linear(model_dim, head_count * self.dim_per_head)
        self.linear_v = nn.Linear(model_dim, head_count * self.dim_per_head)
        self.linear_q = nn.Linear(model_dim, head_count * self.dim_per_head)
        self.softmax = nn.Softmax(dim=-1)
        self.dropout = nn.Dropout(dropout)
        self.linear = nn.Linear(model_dim, model_dim)

        self.eps = 1e-6  # 修改3 防止除以零

    def forward(self, key, value, query, mask=None,return_attn=False):
        batch_size = key.size(0)
        dim_per_head = self.dim_per_head
        head_count = self.head_count

        # def shape(x):
        #     """  projection """
        #     return x.view(batch_size, -1, head_count, dim_per_head).transpose(1, 2)
        #
        # def unshape(x):
        #     """  compute context """
        #     return x.transpose(1, 2).contiguous() \
        #         .view(batch_size, -1, head_count * dim_per_head)

        key = self.linear_k(key).view(batch_size, -1, head_count, dim_per_head).transpose(1, 2)
        value = self.linear_v(value).view(batch_size, -1, head_count, dim_per_head).transpose(1, 2)
        query = self.linear_q(query).view(batch_size, -1, head_count, dim_per_head).transpose(1, 2)

        query = query / math.sqrt(dim_per_head)
        scores = torch.matmul(query, key.transpose(2, 3))

        if mask is not None:
            mask = mask.unsqueeze(1).expand_as(scores)
            #scores = scores.masked_fill(mask, -1e10)
            scores = scores.masked_fill(mask, -1e10)  # 修改3 有限负数代替-inf

        # ----------------- 修改4：稳定softmax计算 -----------------
        max_scores = torch.max(scores, dim=-1, keepdim=True)[0]
        stable_scores = scores - max_scores  # 最大值减法
        attn = self.softmax(stable_scores)

        #attn = self.softmax(scores)
        drop_attn = self.dropout(attn)
        context = torch.matmul(drop_attn, value).transpose(1, 2). \
            contiguous().view(batch_size, -1, head_count * dim_per_head)
        output = self.linear(context)

        if return_attn:
            # 将多头attention拼接成三维，和output生成方式一样
            # (batch, head_count, seq_len, seq_len) -> (batch, seq_len, head_count * seq_len)
            seq_len = attn.size(-1)  # 获取序列长度
            attn_concat = attn.transpose(1, 2).contiguous().view(batch_size, -1, head_count * seq_len)
            return output, attn_concat
        else:
            return output


class PositionalEncoding(nn.Module):
    def __init__(self, dim, max_len=512):
        super(PositionalEncoding, self).__init__()
        self.dim = dim
        
    def forward(self, x, speaker_emb):
        batch_size, seq_len, d_model = x.size()
        device = x.device
        
        # 直接在forward中计算位置编码
        pe = torch.zeros(seq_len, d_model, device=device)
        position = torch.arange(0, seq_len, device=device).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2, device=device, dtype=torch.float) * 
            -(math.log(10000.0) / d_model)
        )
        
        pe[:, 0::2] = torch.sin(position.float() * div_term)
        pe[:, 1::2] = torch.cos(position.float() * div_term)
        pe = pe.unsqueeze(0)  # [1, seq_len, d_model]
        
        # # 检查speaker_emb的维度并做相应处理
        # if speaker_emb.dim() == 2:
        #     # 先进行reshape以匹配batch_size
        #     if speaker_emb.size(0) % batch_size == 0:
        #         # 如果speaker_emb的batch大小是当前batch大小的整数倍，取前batch_size个
        #         speaker_emb = speaker_emb[:batch_size]
        #         speaker_emb = speaker_emb.unsqueeze(1).expand(batch_size, seq_len, d_model)
        #     else:
        #         #  
        #         print(f"Warning: speaker_emb batch size {speaker_emb.size(0)} not compatible with x batch size {batch_size}")
        #         # 创建一个全零的嵌入作为替代
        #         speaker_emb = torch.zeros(batch_size, seq_len, d_model, device=device)
        # else:
        #     # 确保batch大小匹配
        #     if speaker_emb.size(0) != batch_size:
        #         # 处理batch大小不匹配的情况
        #         if speaker_emb.size(0) % batch_size == 0:
        #             speaker_emb = speaker_emb[:batch_size]
        #         else:
        #             print(f"Warning: speaker_emb batch size {speaker_emb.size(0)} not compatible with x batch size {batch_size}")
        #             # 创建一个全零的嵌入作为替代
        #             speaker_emb = torch.zeros(batch_size, seq_len, d_model, device=device)
            
        #     # 确保speaker_emb的第1维(序列长度)与x相同
        #     if speaker_emb.size(1) > seq_len:
        #         # 如果speaker_emb太长，截断它
        #         speaker_emb = speaker_emb[:, :seq_len, :]
        #     elif speaker_emb.size(1) < seq_len:
        #         # 如果speaker_emb太短，填充它
        #         padding = torch.zeros(speaker_emb.size(0), seq_len - speaker_emb.size(1), speaker_emb.size(2), device=device)
        #         speaker_emb = torch.cat([speaker_emb, padding], dim=1)

        # 确保speaker_emb的第1维(序列长度)与x相同
        #print(speaker_emb.size(),x.size(),pe.size()) #torch.Size([16, 69, 1024]) torch.Size([16, 69, 1024]) torch.Size([1, 69, 1024])
        # speaker_emb = speaker_emb.transpose(0,1) #74,16,1024 -> 16,74,1024
        if speaker_emb.size(1) > seq_len:
            # 如果speaker_emb太长，截断它
            speaker_emb = speaker_emb[:, :seq_len, :]
        elif speaker_emb.size(1) < seq_len:
            # 如果speaker_emb太短，填充它
            padding = torch.zeros(speaker_emb.size(0), seq_len - speaker_emb.size(1), speaker_emb.size(2), device=device)
            speaker_emb = torch.cat([speaker_emb, padding], dim=1)
        
        x = x + pe + speaker_emb
        return x


class TransformerEncoderLayer(nn.Module):
    def __init__(self, d_model, heads, d_ff, dropout):
        super(TransformerEncoderLayer, self).__init__()
        self.self_attn = MultiHeadedAttention(
            heads, d_model, dropout=dropout)
        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        self.dropout = nn.Dropout(dropout)
        # ----------------- 修改5：强制层归一化 -----------------
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)  # 确保归一化

    def forward(self, iter, inputs_a, inputs_b, mask):
        inputs_b = self.layer_norm(inputs_b)  # 强制归一化
        if inputs_a.equal(inputs_b):
            if (iter != 0):
                inputs_b = self.layer_norm(inputs_b)
            else:
                inputs_b = inputs_b

            mask = mask.unsqueeze(1)
            context = self.self_attn(inputs_b, inputs_b, inputs_b, mask=mask)
        else:
            if (iter != 0):
                inputs_b = self.layer_norm(inputs_b)
            else:
                inputs_b = inputs_b

            mask = mask.unsqueeze(1)
            context = self.self_attn(inputs_a, inputs_a, inputs_b, mask=mask)

        out = self.dropout(context) + inputs_b
        return self.feed_forward(out)


class TransformerEncoder(nn.Module):
    def __init__(self, d_model, d_ff, heads, layers, dropout=0.1):
        super(TransformerEncoder, self).__init__()

        # ----------------- 修改6：投影层初始化 -----------------
        self.pos_emb = PositionalEncoding(d_model)
        self.dropout = nn.Dropout(dropout)

        # 初始化投影层权重
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0.01)  # 防止dead neuron

        self.d_model = d_model
        self.layers = layers
        self.pos_emb = PositionalEncoding(d_model)
        self.transformer_inter = nn.ModuleList(
            [TransformerEncoderLayer(d_model, heads, d_ff, dropout)
             for _ in range(layers)])
        self.dropout = nn.Dropout(dropout)

    def forward(self, x_a, x_b, mask, speaker_emb):
        if x_a.equal(x_b):
            x_b = self.pos_emb(x_b, speaker_emb)
            x_b = self.dropout(x_b)
            for i in range(self.layers):
                x_b = self.transformer_inter[i](i, x_b, x_b, mask.eq(0))
        else:
            x_a = self.pos_emb(x_a, speaker_emb)
            x_a = self.dropout(x_a)
            x_b = self.pos_emb(x_b, speaker_emb)
            x_b = self.dropout(x_b)
            for i in range(self.layers):
                x_b = self.transformer_inter[i](i, x_a, x_b, mask.eq(0))
        return x_b


class MultiModalContrastiveLoss(nn.Module):
    """
    多模态对比损失，维护两个模态的动态队列
    """
    def __init__(self, hidden_dim, queue_size=64, temperature=0.07):
        super(MultiModalContrastiveLoss, self).__init__()
        self.queue_size = queue_size
        self.temperature = temperature
        self.hidden_dim = hidden_dim

        # 为两个模态维护队列 (queue_size, hidden_dim)
        self.register_buffer("text_queue", torch.randn(queue_size, hidden_dim))
        self.register_buffer("audio_queue", torch.randn(queue_size, hidden_dim))

        # 队列指针，指向下一个要替换的位置
        self.register_buffer("queue_ptr", torch.zeros(1, dtype=torch.long))
        self.register_buffer("queue_size_current", torch.zeros(1, dtype=torch.long))

        # 初始化队列
        self.text_queue = F.normalize(self.text_queue, dim=1)
        self.audio_queue = F.normalize(self.audio_queue, dim=1)

        # 延迟更新标志
        self._has_pending_update = False

    def _dequeue_and_enqueue(self, text_features, audio_features):
        """
        更新队列：先进后出
        """
        batch_size = text_features.shape[0]

        # 确保特征已归一化，并断开梯度
        text_features = F.normalize(text_features.detach(), dim=1)
        audio_features = F.normalize(audio_features.detach(), dim=1)

        # 更新队列 - 使用非in-place操作
        ptr = int(self.queue_ptr)

        # 如果队列未满，直接添加
        if self.queue_size_current < self.queue_size:
            end_ptr = min(ptr + batch_size, self.queue_size)
            actual_batch_size = end_ptr - ptr

            # 创建新的队列tensor
            new_text_queue = self.text_queue.clone()
            new_audio_queue = self.audio_queue.clone()

            new_text_queue[ptr:end_ptr] = text_features[:actual_batch_size]
            new_audio_queue[ptr:end_ptr] = audio_features[:actual_batch_size]

            # 替换整个buffer
            self.text_queue.data = new_text_queue.data
            self.audio_queue.data = new_audio_queue.data

            self.queue_size_current = min(self.queue_size_current + actual_batch_size, self.queue_size)
            self.queue_ptr[0] = end_ptr % self.queue_size
        else:
            # 队列已满，循环替换
            new_text_queue = self.text_queue.clone()
            new_audio_queue = self.audio_queue.clone()

            if ptr + batch_size <= self.queue_size:
                new_text_queue[ptr:ptr + batch_size] = text_features
                new_audio_queue[ptr:ptr + batch_size] = audio_features
            else:
                # 分两部分更新
                part1_size = self.queue_size - ptr
                part2_size = batch_size - part1_size

                new_text_queue[ptr:] = text_features[:part1_size]
                new_text_queue[:part2_size] = text_features[part1_size:]

                new_audio_queue[ptr:] = audio_features[:part1_size]
                new_audio_queue[:part2_size] = audio_features[part1_size:]

            # 替换整个buffer
            self.text_queue.data = new_text_queue.data
            self.audio_queue.data = new_audio_queue.data

            self.queue_ptr[0] = (ptr + batch_size) % self.queue_size

    def forward(self, text_features, audio_features, mask=None):
        """
        计算三种对比损失

        Args:
            text_features: (batch_size, seq_len, hidden_dim) 或 (batch_size, hidden_dim)
            audio_features: (batch_size, seq_len, hidden_dim) 或 (batch_size, hidden_dim)
            mask: (batch_size, seq_len) 掩码

        Returns:
            loss1: 自对比损失 (同模态内部对比)
            loss2: 交叉对比损失 (不同模态间对比)
            loss3: 模态间一致性损失
        """
        # 处理延迟的队列更新
        if hasattr(self, '_has_pending_update') and self._has_pending_update:
            with torch.no_grad():
                self._dequeue_and_enqueue(self._pending_text_features, self._pending_audio_features)
                self._has_pending_update = False
        # 处理三维输入，池化为二维
        if text_features.dim() == 3:
            if mask is not None:
                # 带掩码的平均池化
                mask_expanded = mask.unsqueeze(-1).expand_as(text_features)
                text_masked = text_features * mask_expanded
                valid_lengths = mask.sum(dim=1, keepdim=True).float()
                valid_lengths = torch.clamp(valid_lengths, min=1.0)
                text_features = text_masked.sum(dim=1) / valid_lengths
            else:
                text_features = text_features.mean(dim=1)

        if audio_features.dim() == 3:
            if mask is not None:
                mask_expanded = mask.unsqueeze(-1).expand_as(audio_features)
                audio_masked = audio_features * mask_expanded
                valid_lengths = mask.sum(dim=1, keepdim=True).float()
                valid_lengths = torch.clamp(valid_lengths, min=1.0)
                audio_features = audio_masked.sum(dim=1) / valid_lengths
            else:
                audio_features = audio_features.mean(dim=1)

        batch_size = text_features.shape[0]
        current_queue_size = int(self.queue_size_current)

        # 归一化特征
        text_features = F.normalize(text_features, dim=1)
        audio_features = F.normalize(audio_features, dim=1)

        # 获取当前有效的队列
        if current_queue_size == 0:
            # 第一次：使用当前batch作为队列
            text_queue = text_features.detach()  # 断开梯度
            audio_queue = audio_features.detach()
            current_queue_size = batch_size
        else:
            text_queue = self.text_queue[:current_queue_size].detach()  # 断开梯度
            audio_queue = self.audio_queue[:current_queue_size].detach()

        # 1. 计算自对比矩阵
        # 文本自对比: (batch_size, queue_size)
        text_self_sim = torch.mm(text_features, text_queue.T) / self.temperature
        audio_self_sim = torch.mm(audio_features, audio_queue.T) / self.temperature

        # 2. 计算交叉对比矩阵
        # 矩阵3: 文本交叉对比矩阵 - 文本特征 vs 音频队列
        text_cross_sim = torch.mm(text_features, audio_queue.T) / self.temperature
        # 矩阵4: 音频交叉对比矩阵 - 音频特征 vs 文本队列
        audio_cross_sim = torch.mm(audio_features, text_queue.T) / self.temperature

        # 3. 计算损失

        # Loss 1: 自对比损失 (同模态内部应该不相似)
        # 掩盖对角线元素（自己和自己）
        if current_queue_size >= batch_size:
            mask_self = torch.eye(batch_size, device=text_features.device, dtype=torch.bool)
            # 创建掩盖后的相似度矩阵用于损失计算
            text_self_sim_masked = text_self_sim.clone()
            audio_self_sim_masked = audio_self_sim.clone()
            text_self_sim_masked[:, :batch_size].masked_fill_(mask_self, 0.0)
            audio_self_sim_masked[:, :batch_size].masked_fill_(mask_self, 0.0)

            # 计算非对角线元素的平均值，希望接近0
            loss1_text = torch.mean(torch.abs(text_self_sim_masked))
            loss1_audio = torch.mean(torch.abs(audio_self_sim_masked))
        else:
            # 队列还没满，直接计算
            loss1_text = torch.mean(torch.abs(text_self_sim))
            loss1_audio = torch.mean(torch.abs(audio_self_sim))

        loss1 = (loss1_text + loss1_audio) / 2

        # Loss 2: 交叉对比损失 (对应位置应该相似)
        # 创建正样本标签：当前batch在队列中的对应位置
        if current_queue_size >= batch_size:
            # 正样本是队列前batch_size个位置
            positive_labels = torch.arange(batch_size, device=text_features.device)
            loss2_ta = F.cross_entropy(text_cross_sim, positive_labels)
            loss2_at = F.cross_entropy(audio_cross_sim, positive_labels)
        else:
            # 队列还没满，使用当前所有位置作为正样本
            # 这种情况下，每个样本的正样本就是它自己在队列中的位置
            positive_labels = torch.arange(min(batch_size, current_queue_size), device=text_features.device)
            if batch_size > current_queue_size:
                # 如果batch_size大于队列大小，只计算前current_queue_size个样本的损失
                loss2_ta = F.cross_entropy(text_cross_sim[:current_queue_size], positive_labels)
                loss2_at = F.cross_entropy(audio_cross_sim[:current_queue_size], positive_labels)
            else:
                loss2_ta = F.cross_entropy(text_cross_sim, positive_labels)
                loss2_at = F.cross_entropy(audio_cross_sim, positive_labels)

        loss2 = (loss2_ta + loss2_at) / 2

        # Loss 3: 模态间一致性损失
        # 包括两部分：
        # 3.1 不同模态自对比矩阵之间的损失
        loss3_self = F.mse_loss(text_self_sim, audio_self_sim)

        # 3.2 不同模态交叉对比矩阵之间的损失
        loss3_cross = F.mse_loss(text_cross_sim, audio_cross_sim)

        # 总的模态间一致性损失
        loss3 = (loss3_self + loss3_cross) / 2

        # 延迟队列更新到下一次forward调用，避免梯度问题
        # 保存当前特征用于下次更新
        self._pending_text_features = text_features.detach()
        self._pending_audio_features = audio_features.detach()
        self._has_pending_update = True

        return loss1, loss2, loss3


class Unimodal_GatedFusion(nn.Module):
    def __init__(self, hidden_size, dataset):
        super(Unimodal_GatedFusion, self).__init__()
        self.fc = nn.Linear(hidden_size, hidden_size, bias=False)
        if dataset == 'MELD':
            self.fc.weight.data.copy_(torch.eye(hidden_size, hidden_size))
            self.fc.weight.requires_grad = False

    def forward(self, a):
        z = torch.sigmoid(self.fc(a))
        final_rep = z * a
        return final_rep


class Multimodal_GatedFusion(nn.Module):
    def __init__(self, hidden_size):
        super(Multimodal_GatedFusion, self).__init__()
        # # ----------------- 修改7：添加残差系数 -----------------
        # self.fc = nn.Linear(hidden_size, hidden_size, bias=False)
        # self.alpha = nn.Parameter(torch.tensor(0.1))  # 可学习残差系数
        self.fc = nn.Linear(hidden_size, hidden_size, bias=False)
        self.softmax = nn.Softmax(dim=-2)

    def forward(self, a, b, c):
        a_new = a.unsqueeze(-2)
        b_new = b.unsqueeze(-2)
        c_new = c.unsqueeze(-2)
        utters = torch.cat([a_new, b_new, c_new], dim=-2)
        utters_fc = torch.cat([self.fc(a).unsqueeze(-2), self.fc(b).unsqueeze(-2), self.fc(c).unsqueeze(-2)], dim=-2)
        utters_softmax = self.softmax(utters_fc)
        utters_three_model = utters_softmax * utters
        final_rep = torch.sum(utters_three_model, dim=-2, keepdim=False)
        return final_rep


class Transformer_Based_Model(nn.Module):
    def __init__(self, dataset, temp, D_text, D_visual, D_audio, n_head,
                 n_classes, hidden_dim, n_speakers, dropout):
        super(Transformer_Based_Model, self).__init__()
        self.temp = temp
        self.n_classes = n_classes
        self.n_speakers = n_speakers
        if self.n_speakers == 2:
            padding_idx = 2
        if self.n_speakers == 9:
            padding_idx = 9
        self.speaker_embeddings = nn.Embedding(n_speakers + 1, hidden_dim, padding_idx=padding_idx)

        # Temporal convolutional layers
        self.textf_input = nn.Conv1d(D_text, hidden_dim, kernel_size=1, padding=0, bias=False)
        self.acouf_input = nn.Conv1d(D_audio, hidden_dim, kernel_size=1, padding=0, bias=False)
        self.visuf_input = nn.Conv1d(D_visual, hidden_dim, kernel_size=1, padding=0, bias=False)

        # Intra- and Inter-modal Transformers
        self.t_t = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.a_t = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.v_t = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)

        self.a_a = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.t_a = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.v_a = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)

        self.v_v = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.t_v = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)
        self.a_v = TransformerEncoder(d_model=hidden_dim, d_ff=hidden_dim, heads=n_head, layers=1, dropout=dropout)

        # Unimodal-level Gated Fusion
        self.t_t_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.a_t_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.v_t_gate = Unimodal_GatedFusion(hidden_dim, dataset)

        self.a_a_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.t_a_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.v_a_gate = Unimodal_GatedFusion(hidden_dim, dataset)

        self.v_v_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.t_v_gate = Unimodal_GatedFusion(hidden_dim, dataset)
        self.a_v_gate = Unimodal_GatedFusion(hidden_dim, dataset)

        self.features_reduce_t = nn.Linear(3 * hidden_dim, hidden_dim)
        self.features_reduce_a = nn.Linear(3 * hidden_dim, hidden_dim)
        self.features_reduce_v = nn.Linear(3 * hidden_dim, hidden_dim)

        # Multimodal-level Gated Fusion
        self.last_gate = Multimodal_GatedFusion(hidden_dim)

        # Multi-Modal Contrastive Loss
        self.contrastive_loss = MultiModalContrastiveLoss(hidden_dim, queue_size=64, temperature=0.07)

        # Emotion Classifier
        self.t_output_layer = nn.Sequential(
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, n_classes)
        )
        self.a_output_layer = nn.Sequential(
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, n_classes)
        )
        self.v_output_layer = nn.Sequential(
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, n_classes)
        )
        self.all_output_layer = nn.Linear(hidden_dim, n_classes)

    def forward(self, textf, visuf, acouf, u_mask, qmask, dia_len):
        #qmask = torch.cat([qmask[:x, i, :] for i, x in enumerate(dia_len)], dim=0)  #对齐qmask 74,16，2 ->702，2
        #spk_idx = torch.argmax(qmask, dim=-1) #702
        spk_idx = torch.argmax(qmask, dim=-1)
        # origin_spk_idx = spk_idx 
        # if self.n_speakers == 2:
        #     for i, x in enumerate(dia_len):
        #         spk_idx[i, x:] = (2 * torch.ones(origin_spk_idx[i].size(0) - x)).int().cuda()
        # if self.n_speakers == 9:
        #     for i, x in enumerate(dia_len):
        #         spk_idx[i, x:] = (9 * torch.ones(origin_spk_idx[i].size(0) - x)).int().cuda()
        spk_embeddings = self.speaker_embeddings(spk_idx) #74，16，1024

        # Temporal convolutional layers
        textf = self.textf_input(textf.permute(1, 2, 0)).transpose(1, 2) #->16 72 1024
        acouf = self.acouf_input(acouf.permute(1, 2, 0)).transpose(1, 2)
        visuf = self.visuf_input(visuf.permute(1, 2, 0)).transpose(1, 2)

        # Intra- and Inter-modal Transformers
        t_t_transformer_out = self.t_t(textf, textf, u_mask, spk_embeddings)
        a_t_transformer_out = self.a_t(acouf, textf, u_mask, spk_embeddings)
        v_t_transformer_out = self.v_t(visuf, textf, u_mask, spk_embeddings)

        a_a_transformer_out = self.a_a(acouf, acouf, u_mask, spk_embeddings)
        t_a_transformer_out = self.t_a(textf, acouf, u_mask, spk_embeddings)
        v_a_transformer_out = self.v_a(visuf, acouf, u_mask, spk_embeddings)

        v_v_transformer_out = self.v_v(visuf, visuf, u_mask, spk_embeddings)
        t_v_transformer_out = self.t_v(textf, visuf, u_mask, spk_embeddings)
        a_v_transformer_out = self.a_v(acouf, visuf, u_mask, spk_embeddings)

        # Multi-Modal Contrastive Loss Calculation
        # 使用t_t_transformer_out和a_a_transformer_out计算对比损失
        contrastive_loss1, contrastive_loss2, contrastive_loss3 = self.contrastive_loss(
            t_t_transformer_out, a_a_transformer_out, u_mask
        )

        # Unimodal-level Gated Fusion
        t_t_transformer_out = self.t_t_gate(t_t_transformer_out)
        a_t_transformer_out = self.a_t_gate(a_t_transformer_out)
        v_t_transformer_out = self.v_t_gate(v_t_transformer_out)

        a_a_transformer_out = self.a_a_gate(a_a_transformer_out)
        t_a_transformer_out = self.t_a_gate(t_a_transformer_out)
        v_a_transformer_out = self.v_a_gate(v_a_transformer_out)

        v_v_transformer_out = self.v_v_gate(v_v_transformer_out)
        t_v_transformer_out = self.t_v_gate(t_v_transformer_out)
        a_v_transformer_out = self.a_v_gate(a_v_transformer_out)

        t_transformer_out = self.features_reduce_t(
            torch.cat([t_t_transformer_out, a_t_transformer_out, v_t_transformer_out], dim=-1))
        a_transformer_out = self.features_reduce_a(
            torch.cat([a_a_transformer_out, t_a_transformer_out, v_a_transformer_out], dim=-1))
        v_transformer_out = self.features_reduce_v(
            torch.cat([v_v_transformer_out, t_v_transformer_out, a_v_transformer_out], dim=-1))

        # Multimodal-level Gated Fusion
        all_transformer_out = self.last_gate(t_transformer_out, a_transformer_out, v_transformer_out)

        # Emotion Classifier
        t_final_out = self.t_output_layer(t_transformer_out)
        a_final_out = self.a_output_layer(a_transformer_out)
        v_final_out = self.v_output_layer(v_transformer_out)
        all_final_out = self.all_output_layer(all_transformer_out)

        t_log_prob = F.log_softmax(t_final_out, 2)
        a_log_prob = F.log_softmax(a_final_out, 2)
        v_log_prob = F.log_softmax(v_final_out, 2)

        all_log_prob = F.log_softmax(all_final_out, 2)
        all_prob = F.softmax(all_final_out, 2)

        kl_t_log_prob = F.log_softmax(t_final_out / self.temp, 2)
        kl_a_log_prob = F.log_softmax(a_final_out / self.temp, 2)
        kl_v_log_prob = F.log_softmax(v_final_out / self.temp, 2)

        kl_all_prob = F.softmax(all_final_out / self.temp, 2)

        return t_log_prob, a_log_prob, v_log_prob, all_log_prob, all_prob, kl_t_log_prob, kl_a_log_prob, kl_v_log_prob, kl_all_prob, contrastive_loss1, contrastive_loss2, contrastive_loss3


