import torch
import torch.nn as nn
import torch.nn.functional as F


class IntraBatchContrastiveLoss(nn.Module):
    """
    批内对比损失，在每个batch内维持滑动窗口队列
    """
    def __init__(self, hidden_dim, queue_size=10, temperature=0.07):
        super(IntraBatchContrastiveLoss, self).__init__()
        self.queue_size = queue_size
        self.temperature = temperature
        self.hidden_dim = hidden_dim

    def forward(self, text_features, audio_features, mask=None):
        """
        批内对比损失计算

        Args:
            text_features: (batch_size, seq_len, hidden_dim)
            audio_features: (batch_size, seq_len, hidden_dim)
            mask: (batch_size, seq_len) 掩码，1表示有效位置

        Returns:
            intra_modal_loss: 模态内对比损失
            inter_modal_loss: 模态间对比损失
        """
        batch_size, seq_len, hidden_dim = text_features.shape

        # 归一化特征
        text_features = F.normalize(text_features, dim=-1)
        audio_features = F.normalize(audio_features, dim=-1)

        total_intra_loss = 0.0
        total_inter_loss = 0.0
        total_count = 0

        # 对每个样本分别处理
        for b in range(batch_size):
            # 获取当前样本的有效长度
            if mask is not None:
                valid_length = int(mask[b].sum().item())
            else:
                valid_length = int(seq_len)

            if valid_length <= 1:
                continue  # 跳过长度太短的序列

            # 获取当前样本的有效特征
            text_seq = text_features[b, :valid_length, :]  # (valid_length, hidden_dim)
            audio_seq = audio_features[b, :valid_length, :]  # (valid_length, hidden_dim)

            # 对每个位置计算对比损失
            for i in range(valid_length):
                # 当前位置的特征
                text_current = text_seq[i:i+1, :]  # (1, hidden_dim)
                audio_current = audio_seq[i:i+1, :]  # (1, hidden_dim)

                # 构建队列：取前面的特征作为队列（最多queue_size个）
                queue_start = max(0, i - self.queue_size + 1)
                queue_end = i+1

                if queue_end > queue_start:  # 确保队列不为空
                    # 文本队列和音频队列
                    text_queue = text_seq[queue_start:queue_end, :]  # (queue_len, hidden_dim)
                    audio_queue = audio_seq[queue_start:queue_end, :]  # (queue_len, hidden_dim)
                    queue_len = queue_end - queue_start

                    # 1. 模态内对比损失
                    # 文本当前位置 vs 文本队列
                    text_sim = torch.mm(text_current, text_queue.T)  # (1, queue_len)
                    # 音频当前位置 vs 音频队列
                    audio_sim = torch.mm(audio_current, audio_queue.T) # 1*3 1*3->1*1  (1, queue_len)

                    # 标签全为0（所有队列位置都是负样本）
                    intra_labels = torch.zeros(queue_len, device=text_features.device, dtype=torch.long)

                    # 计算交叉熵损失（希望与队列中所有位置的相似度都低）
                    text_intra_loss = F.cross_entropy(text_sim, intra_labels)
                    audio_intra_loss = F.cross_entropy(audio_sim, intra_labels)

                    total_intra_loss += (text_intra_loss + audio_intra_loss)

                    # 2. 模态间对比损失
                    # 文本当前位置 vs 音频队列
                    text_cross_sim = torch.mm(text_current, audio_queue.T)  # (1, queue_len)
                    # 音频当前位置 vs 文本队列
                    audio_cross_sim = torch.mm(audio_current, text_queue.T)   # (1, queue_len)

                    # 标签：最后一个位置为1（最近的位置是正样本），其余为0
                    inter_labels = torch.tensor([queue_len - 1], device=text_features.device, dtype=torch.long)

                    # 计算交叉熵损失
                    text_inter_loss = F.cross_entropy(text_cross_sim, inter_labels)
                    audio_inter_loss = F.cross_entropy(audio_cross_sim, inter_labels)

                    total_inter_loss += (text_inter_loss + audio_inter_loss)

                    total_count += 1

        # 计算平均损失
        if total_count > 0:
            intra_modal_loss = total_intra_loss / total_count
            inter_modal_loss = total_inter_loss / total_count
        else:
            intra_modal_loss = torch.tensor(0.0, device=text_features.device, requires_grad=True)
            inter_modal_loss = torch.tensor(0.0, device=text_features.device, requires_grad=True)

        return intra_modal_loss, inter_modal_loss


def create_fixed_test_data():
    """创建固定的测试数据"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 固定参数
    batch_size = 2
    max_seq_len = 3
    hidden_dim = 3
    
    print(f"使用设备: {device}")
    print(f"数据形状: batch_size={batch_size}, max_seq_len={max_seq_len}, hidden_dim={hidden_dim}")
    
    # 手工创建特征矩阵
    text_features = torch.zeros(batch_size, max_seq_len, hidden_dim, device=device)
    audio_features = torch.zeros(batch_size, max_seq_len, hidden_dim, device=device)
    
    # 样本1的文本特征 (长度=3)
    text_features[0, 0, :] = torch.tensor([1.0, 0.0, 0.0], device=device)  # 位置0
    text_features[0, 1, :] = torch.tensor([0.0, 1.0, 0.0], device=device)  # 位置1
    text_features[0, 2, :] = torch.tensor([0.0, 0.0, 1.0], device=device)  # 位置2
    
    # 样本1的音频特征 (与文本相似但略有不同)
    audio_features[0, 0, :] = torch.tensor([0.9, 0.1, 0.0], device=device)
    audio_features[0, 1, :] = torch.tensor([0.1, 0.9, 0.0], device=device)
    audio_features[0, 2, :] = torch.tensor([0.0, 0.1, 0.9], device=device)
    
    # 样本2的文本特征 (长度=2)
    text_features[1, 0, :] = torch.tensor([0.5, 0.5, 0.0], device=device)
    text_features[1, 1, :] = torch.tensor([0.0, 0.5, 0.5], device=device)
    
    # 样本2的音频特征
    audio_features[1, 0, :] = torch.tensor([0.6, 0.4, 0.0], device=device)
    audio_features[1, 1, :] = torch.tensor([0.0, 0.4, 0.6], device=device)
    
    # 创建掩码
    umask = torch.zeros(batch_size, max_seq_len, device=device)
    umask[0, :3] = 1  # 样本1前3个位置有效
    umask[1, :2] = 1  # 样本2前2个位置有效
    
    print("\n固定测试数据:")
    print("样本1文本特征:")
    for i in range(3):
        print(f"  位置{i}: {text_features[0, i, :].tolist()}")
    print("样本1音频特征:")
    for i in range(3):
        print(f"  位置{i}: {audio_features[0, i, :].tolist()}")
    print("样本2文本特征:")
    for i in range(2):
        print(f"  位置{i}: {text_features[1, i, :].tolist()}")
    print("样本2音频特征:")
    for i in range(2):
        print(f"  位置{i}: {audio_features[1, i, :].tolist()}")
    print(f"掩码: {umask.tolist()}")
    
    return text_features, audio_features, umask


def test_contrastive_loss():
    """测试对比损失函数"""
    print("=" * 60)
    print("对比损失函数测试")
    print("=" * 60)
    
    # 创建测试数据
    text_features, audio_features, umask = create_fixed_test_data()
    
    # 测试不同的温度参数
    temperatures = [0.01, 0.1, 0.5, 1.0]
    queue_size = 2
    
    for temp in temperatures:
        print(f"\n--- 温度参数: {temp} ---")
        
        # 创建对比损失函数
        contrastive_loss = IntraBatchContrastiveLoss(
            hidden_dim=3,
            queue_size=queue_size,
            temperature=temp
        ).to(text_features.device)
        
        try:
            # 计算损失
            intra_loss, inter_loss = contrastive_loss(text_features, audio_features, umask)
            
            print(f"模态内损失: {intra_loss.item():.6f}")
            print(f"模态间损失: {inter_loss.item():.6f}")
            
            # 检查负损失
            if intra_loss.item() < 0:
                print("⚠️  模态内损失为负!")
            if inter_loss.item() < 0:
                print("⚠️  模态间损失为负!")
                
            # 测试梯度
            total_loss = intra_loss + inter_loss
            if text_features.requires_grad:
                total_loss.backward(retain_graph=True)
                print(f"梯度计算成功")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    # 设置需要梯度
    torch.manual_seed(42)  # 固定随机种子
    test_contrastive_loss()
