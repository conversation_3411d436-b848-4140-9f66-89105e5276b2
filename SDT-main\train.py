import os
import warnings

# 过滤掉NumPy数组列表转换为张量的警告
warnings.filterwarnings("ignore", message="Creating a tensor from a list of numpy.ndarrays is extremely slow")
from torch import nn

os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import numpy as np, argparse, time
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.data.sampler import SubsetRandomSampler
from dataloader import IEMOCAPDataset, MELDDataset
from model import Masked<PERSON><PERSON>oss, MaskedKLDivLoss, Transformer_Based_Model
from sklearn.metrics import f1_score, confusion_matrix, accuracy_score, classification_report
import pickle as pk
import datetime
from detailed_evaluation import detailed_evaluation_with_weights


def get_train_valid_sampler(trainset, valid=0.1, dataset='IEMOCAP'):
    size = len(trainset)
    idx = list(range(size))
    split = int(valid * size)
    return SubsetRandomSampler(idx[split:]), SubsetRandomSampler(idx[:split])


def get_MELD_loaders(batch_size=32, valid=0.1, num_workers=0, pin_memory=False):
    trainset = MELDDataset('data/meld_multimodal_features.pkl')
    train_sampler, valid_sampler = get_train_valid_sampler(trainset, valid, 'MELD')
    train_loader = DataLoader(trainset,
                              batch_size=batch_size,
                              sampler=train_sampler,
                              collate_fn=trainset.collate_fn,
                              num_workers=num_workers,
                              pin_memory=pin_memory)
    valid_loader = DataLoader(trainset,
                              batch_size=batch_size,
                              sampler=valid_sampler,
                              collate_fn=trainset.collate_fn,
                              num_workers=num_workers,
                              pin_memory=pin_memory)

    testset = MELDDataset('data/meld_multimodal_features.pkl', train=False)
    test_loader = DataLoader(testset,
                             batch_size=batch_size,
                             collate_fn=testset.collate_fn,
                             num_workers=num_workers,
                             pin_memory=pin_memory)
    return train_loader, valid_loader, test_loader


def get_IEMOCAP_loaders(batch_size=32, valid=0.1, num_workers=0, pin_memory=False):
    trainset = IEMOCAPDataset()
    train_sampler, valid_sampler = get_train_valid_sampler(trainset, valid)
    train_loader = DataLoader(trainset,
                              batch_size=batch_size,
                              sampler=train_sampler,
                              collate_fn=trainset.collate_fn,
                              num_workers=num_workers,
                              pin_memory=pin_memory)
    valid_loader = DataLoader(trainset,
                              batch_size=batch_size,
                              sampler=valid_sampler,
                              collate_fn=trainset.collate_fn,
                              num_workers=num_workers,
                              pin_memory=pin_memory)

    testset = IEMOCAPDataset(train=False)
    test_loader = DataLoader(testset,
                             batch_size=batch_size,
                             collate_fn=testset.collate_fn,
                             num_workers=num_workers,
                             pin_memory=pin_memory)
    return train_loader, valid_loader, test_loader


def train_or_eval_model(model, loss_function, kl_loss, dataloader, epoch, optimizer=None, train=False, gamma_1=1.0,
                        gamma_2=1.0, gamma_3=1.0,dataset='IEMOCAP'):
    losses, preds, labels= [], [], []
    vids = []

    assert not train or optimizer != None
    if train:
        model.train()
    else:
        model.eval()

    for data in dataloader:
        if train:
            optimizer.zero_grad()
        # umask (batch_size, seq_len)
        textf, visuf, acouf, qmask, umask, label = [d.cuda() for d in data[:-1]] if cuda else data[:-1]
        qmask = qmask.permute(1, 0, 2)  # ->(batch_size, max_seq_len, 2)    
        lengths = [(umask[j] == 1).nonzero().tolist()[-1][0] + 1 for j in range(len(umask))]

        log_prob1, log_prob2, log_prob3, all_log_prob, all_prob, kl_log_prob1, kl_log_prob2, kl_log_prob3, kl_all_prob, contrastive_loss1, contrastive_loss2 = model(textf, visuf, acouf, umask, qmask, lengths)
        # print("------------------------------------------------------")
        # print(contrastive_loss1)
        # print(contrastive_loss2)
        # print(contrastive_loss3)
        #all_log_prob = model(textf, visuf, acouf, umask, qmask, lengths) #16,79,6

        #prob_all = all_log_prob.view(-1, all_log_prob.size()[2]) #(16,79,6) -> (1264,6)

        #只收集有效长度的预测标签
        prob_all = torch.cat([all_prob[i, :x, :] for i, x in enumerate(lengths)], dim=0)  #减少-> ?,6
        # 只收集有效长度的标签，与prob_all保持一致
        label1 = torch.cat([label[i, :x] for i, x in enumerate(lengths)], dim=0)
        


        prob1_log = torch.cat([log_prob1[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        prob2_log = torch.cat([log_prob2[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        prob3_log = torch.cat([log_prob3[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        all_log_prob = torch.cat([all_log_prob[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        kl_log_prob1 = torch.cat([kl_log_prob1[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        kl_log_prob2 = torch.cat([kl_log_prob2[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        kl_log_prob3 = torch.cat([kl_log_prob3[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        kl_all_prob = torch.cat([kl_all_prob[i, :x, :] for i, x in enumerate(lengths)], dim=0)
        # label1_one_hot = nn.functional.one_hot(kl_all_prob, num_classes=7).float()#one-hot编码

        kl_criterion = nn.KLDivLoss(reduction='batchmean')
        
        loss1 = loss_function(all_log_prob, label1,)  #in: 1264,6 和 1264
        loss2 = loss_function(prob1_log, label1)+loss_function(prob2_log, label1)+loss_function(prob3_log, label1)
        loss3 = kl_criterion(kl_log_prob1, kl_all_prob)+kl_criterion(kl_log_prob2, kl_all_prob)+kl_criterion(kl_log_prob3, kl_all_prob)

        loss=gamma_1*loss1+gamma_2*loss2+gamma_3*loss3+contrastive_loss1+contrastive_loss2
        
        # 添加当前批次的损失值到losses列表
        losses.append(loss.item())

        preds.append(torch.argmax(prob_all, 1).cpu().numpy()) #list 1:1264，2: ..
        labels.append(label1.data.cpu().numpy())  # 使用label1而不是labels_

        if train:
            loss.backward()
            if args.tensorboard:
                for param in model.named_parameters():
                    writer.add_histogram(param[0], param[1].grad, epoch)
            optimizer.step()

    if preds != []:
        preds = np.concatenate(preds) #
        labels = np.concatenate(labels)
    else:
        return float('nan'), float('nan'),  [], [], float('nan') , []

    vids += data[-1]
    labels = np.array(labels)
    preds = np.array(preds)
    vids = np.array(vids)

    # 修复类型错误：直接使用Python内置的sum和len计算平均值
    avg_loss = round(sum(losses) / len(losses), 4) if losses else float('nan')
    avg_accuracy = round(accuracy_score(labels, preds) * 100, 4)
    avg_fscore = round(f1_score(labels, preds, average='weighted') * 100, 4)
    return avg_loss, avg_accuracy, labels, preds,  avg_fscore , vids


if __name__ == '__main__':
    # seed
    torch.manual_seed(123)

    parser = argparse.ArgumentParser()
    parser.add_argument('--no-cuda', action='store_true', default=False, help='does not use GPU')
    parser.add_argument('--lr', type=float, default=0.0001, metavar='LR', help='learning rate')
    parser.add_argument('--l2', type=float, default=0.00001, metavar='L2', help='L2 regularization weight')
    parser.add_argument('--dropout', type=float, default=0.1, metavar='dropout', help='dropout rate')
    parser.add_argument('--batch-size', type=int, default=16, metavar='BS', help='batch size')
    parser.add_argument('--hidden_dim', type=int, default=1024, metavar='hidden_dim', help='output hidden size')
    parser.add_argument('--n_head', type=int, default=8, metavar='n_head', help='number of heads')
    parser.add_argument('--epochs', type=int, default=150, metavar='E', help='number of epochs')
    parser.add_argument('--temp', type=int, default=3, metavar='temp', help='temp')
    parser.add_argument('--tensorboard', action='store_true', default=False, help='Enables tensorboard log')
    parser.add_argument('--class-weight', action='store_true', default=True, help='use class weights')
    parser.add_argument('--Dataset', default='IEMOCAP', help='dataset to train and test')

    args = parser.parse_args()
    today = datetime.datetime.now()
    print(args)

    args.cuda = torch.cuda.is_available() and not args.no_cuda
    if args.cuda:
        print('Running on GPU')
    else:
        print('Running on CPU')

    if args.tensorboard:
        from tensorboardX import SummaryWriter

        writer = SummaryWriter()

    cuda = args.cuda
    n_epochs = args.epochs
    batch_size = args.batch_size
    feat2dim = {'IS10': 1582, 'denseface': 342, 'MELD_audio': 300}
    D_audio = feat2dim['IS10'] if args.Dataset == 'IEMOCAP' else feat2dim['MELD_audio']
    D_visual = feat2dim['denseface']
    D_text = 1024

    D_m = D_audio + D_visual + D_text

    n_speakers = 9 if args.Dataset == 'MELD' else 2
    n_classes = 7 if args.Dataset == 'MELD' else 6 if args.Dataset == 'IEMOCAP' else 1

    print('temp {}'.format(args.temp))

    model = Transformer_Based_Model(args.Dataset, args.temp, D_text, D_visual, D_audio, args.n_head,
                                    n_classes=n_classes,
                                    hidden_dim=args.hidden_dim,
                                    n_speakers=n_speakers,
                                    dropout=args.dropout)

    total_params = sum(p.numel() for p in model.parameters())
    print('total parameters: {}'.format(total_params))
    total_trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print('training parameters: {}'.format(total_trainable_params))

    if cuda:
        model.cuda()

    kl_loss = MaskedKLDivLoss()
    cross_entropy_loss = nn.CrossEntropyLoss()
    kl_loss =nn.KLDivLoss(reduction='batchmean')

    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.l2)
    lr = args.lr

    if args.Dataset == 'MELD':
        loss_function = MaskedNLLLoss()
        train_loader, valid_loader, test_loader = get_MELD_loaders(valid=0.0,
                                                                   batch_size=batch_size,
                                                                   num_workers=0)

    elif args.Dataset == 'IEMOCAP':
        loss_weights = torch.FloatTensor([1 / 0.086747,
                                          1 / 0.144406,
                                          1 / 0.227883,
                                          1 / 0.160585,
                                          1 / 0.127711,
                                          1 / 0.252668])

    if args.Dataset == 'MELD':
        loss_function = MaskedNLLLoss(loss_weights.cuda() if cuda else loss_weights)
    else:
        loss_function = nn.NLLLoss(weight=loss_weights.cuda()if cuda else loss_weights)

    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.l2)

    lr = args.lr

    if args.Dataset == 'MELD':
        train_loader, valid_loader, test_loader = get_MELD_loaders(valid=0.0,
                                                                   batch_size=batch_size,
                                                                   num_workers=2)
    elif args.Dataset == 'IEMOCAP':
        train_loader, valid_loader, test_loader = get_IEMOCAP_loaders(valid=0.0,
                                                                      batch_size=batch_size,
                                                                      num_workers=2)
    else:
        print("There is no such dataset")

    best_fscore, best_loss, best_label, best_pred, best_mask = None, None, None, None, None
    all_fscore, all_acc, all_loss = [], [], []

    for e in range(n_epochs):
        start_time = time.time()

        train_loss, train_acc, _, _,  train_fscore , _  = train_or_eval_model(model, loss_function, kl_loss, train_loader,
                                                                           e,optimizer=optimizer, train=True,dataset=args.Dataset)
        valid_loss, valid_acc, _, _, valid_fscore , _ = train_or_eval_model(model, loss_function, kl_loss,valid_loader,
                                                                           e,dataset=args.Dataset)
        test_loss, test_acc, test_label, test_pred, test_fscore,_ = train_or_eval_model(model, loss_function,
                                                                                                 kl_loss, test_loader,
                                                                                                 e,dataset=args.Dataset)
        all_fscore.append(test_fscore)

        # if best_loss == None or best_loss > test_loss:
        #     best_loss, best_label, best_pred = test_loss, test_label, test_pred

        if best_fscore == None or best_fscore < test_fscore:
            best_fscore = test_fscore
            best_label, best_pred = test_label, test_pred

            # 每次达到最优F1分数时输出详细评估
            print(f"\n*** 新的最优F1分数: {test_fscore:.4f}% (Epoch {e+1}) ***")
            detailed_evaluation_with_weights(best_label, best_pred, dataset=args.Dataset)

        # if best_fscore == None or best_fscore < test_fscore:
        #     best_fscore = test_fscore
        #     best_label, best_pred, best_mask = test_label, test_pred, test_mask

        if args.tensorboard:
            writer.add_scalar('test: accuracy', test_acc, e)
            writer.add_scalar('test: fscore', test_fscore, e)
            writer.add_scalar('train: accuracy', train_acc, e)
            writer.add_scalar('train: fscore', train_fscore, e)


        print(
            'epoch: {}, train_loss: {}, train_acc: {}, train_fscore: {}, test_loss: {}, test_acc: {}, test_fscore: {}, time: {} sec'. \
            format(e + 1, train_loss, train_acc, train_fscore, test_loss, test_acc, test_fscore,
                   round(time.time() - start_time, 2)))
        if (e + 1) % 10 == 0:
            print('----------best F-Score:', max(all_fscore))
            print(classification_report(best_label, best_pred, digits=4))
            print(confusion_matrix(best_label, best_pred))

        # print(
        #     'epoch: {}, train_loss: {}, train_acc: {}, train_fscore: {}, valid_loss: {}, valid_acc: {}, valid_fscore: {}, test_loss: {}, test_acc: {}, test_fscore: {}, time: {} sec'. \
        #     format(e + 1, train_loss, train_acc, train_fscore, valid_loss, valid_acc, valid_fscore, test_loss, test_acc,
        #            test_fscore, round(time.time() - start_time, 2)))
        # if (e + 1) % 10 == 0:
        #     print(classification_report(best_label, best_pred, sample_weight=best_mask, digits=4))
        #     print(confusion_matrix(best_label, best_pred, sample_weight=best_mask))

    # if args.tensorboard:
    #     writer.close()
    #
    # print('Test performance..')
    # print('F-Score: {}'.format(max(all_fscore)))
    # print('F-Score-index: {}'.format(all_fscore.index(max(all_fscore)) + 1))
    #
    # if not os.path.exists("record_{}_{}_{}.pk".format(today.year, today.month, today.day)):
    #     with open("record_{}_{}_{}.pk".format(today.year, today.month, today.day), 'wb') as f:
    #         pk.dump({}, f)
    # with open("record_{}_{}_{}.pk".format(today.year, today.month, today.day), 'rb') as f:
    #     record = pk.load(f)
    # key_ = 'name_'
    # if record.get(key_, False):
    #     record[key_].append(max(all_fscore))
    # else:
    #     record[key_] = [max(all_fscore)]
    # if record.get(key_ + 'record', False):
    #     record[key_ + 'record'].append(classification_report(best_label, best_pred, sample_weight=best_mask, digits=4))
    # else:
    #     record[key_ + 'record'] = [classification_report(best_label, best_pred, sample_weight=best_mask, digits=4)]
    # with open("record_{}_{}_{}.pk".format(today.year, today.month, today.day), 'wb') as f:
    #     pk.dump(record, f)
    #
    # print(classification_report(best_label, best_pred, sample_weight=best_mask, digits=4))
    # print(confusion_matrix(best_label, best_pred, sample_weight=best_mask))
    if args.tensorboard:
        writer.close()

    print('Test performance..')
    print('Best F-Score:', max(all_fscore))
    print(classification_report(best_label, best_pred, digits=4))
    print(confusion_matrix(best_label, best_pred))

    # 调用详细评估函数，包含加权准确率和加权F1
    print("\n" + "="*50)
    print("DETAILED EVALUATION WITH WEIGHTS")
    print("="*50)
    detailed_metrics = detailed_evaluation_with_weights(best_label, best_pred, dataset=args.Dataset)


