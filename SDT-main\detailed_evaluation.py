import numpy as np
from sklearn.metrics import f1_score, accuracy_score



def detailed_evaluation_with_weights(labels, preds, dataset='IEMOCAP'):
    """
    计算测试集的详细评估指标：
    1. 每个类别的准确率（召回率）
    2. 每个类别的F1分数
    3. 总体加权准确率 W_ACC
    4. 总体加权F1 W_F1
    5. 总体F1（不加权，宏平均）
    """

    # 定义类别名称
    if dataset == 'IEMOCAP':
        class_names = ['happy', 'sad', 'neutral', 'angry', 'excited', 'frustrated']
        n_classes = 6
    elif dataset == 'MELD':
        class_names = ['neutral', 'surprise', 'fear', 'sadness', 'joy', 'disgust', 'anger']
        n_classes = 7
    else:
        n_classes = max(max(labels), max(preds)) + 1
        class_names = [f'class_{i}' for i in range(n_classes)]

    # 计算每个类别的准确率（召回率）
    class_accuracies = []
    for i in range(n_classes):
        true_positives = np.sum((labels == i) & (preds == i))
        actual_positives = np.sum(labels == i)
        if actual_positives > 0:
            class_acc = true_positives / actual_positives * 100
            class_accuracies.append(class_acc)
        else:
            class_accuracies.append(0.0)

    # 计算每个类别的F1分数
    f1_scores = f1_score(labels, preds, average=None, zero_division=0) * 100

    # 计算加权准确率 W_ACC (按测试集样本数量加权)
    weighted_acc_sum = 0
    total_samples = len(labels)
    for i in range(n_classes):
        test_samples = np.sum(labels == i)
        if test_samples > 0:
            weight = test_samples / total_samples  # 测试集中该类别的比例作为权重
            weighted_acc_sum += class_accuracies[i] * weight
    w_acc = weighted_acc_sum

    # 计算加权F1 W_F1 (按测试集样本数量加权)
    weighted_f1_sum = 0
    total_samples = len(labels)
    for i in range(n_classes):
        test_samples = np.sum(labels == i)
        if test_samples > 0:
            weight = test_samples / total_samples  # 测试集中该类别的比例作为权重
            weighted_f1_sum += f1_scores[i] * weight
    w_f1 = weighted_f1_sum

    # 计算总体指标
    overall_accuracy = accuracy_score(labels, preds) * 100
    macro_f1 = f1_score(labels, preds, average='macro') * 100  # 宏平均F1（不加权）

    # 手动验证宏平均F1计算（各类别F1的算术平均）
    manual_macro_f1 = np.mean(f1_scores)  # 这应该与sklearn的macro结果一致

    # 输出结果
    print("\n" + "="*60)
    print("测试集详细评估结果")
    print("="*60)

    print("\n每个类别的准确率:")
    for i in range(n_classes):
        samples = np.sum(labels == i)
        print(f"{class_names[i]}: {class_accuracies[i]:.4f}% ({samples} 样本)")

    print("\n每个类别的F1分数:")
    for i in range(n_classes):
        print(f"{class_names[i]}: {f1_scores[i]:.4f}%")

    print("\n总体指标:")
    print(f"总体准确率: {overall_accuracy:.4f}%")
    print(f"加权准确率 (W_ACC, 按测试集分布): {w_acc:.4f}%")
    print(f"总体F1分数 (不加权宏平均): {macro_f1:.4f}%")
    print(f"手动计算宏平均F1: {manual_macro_f1:.4f}%")
    print(f"加权F1 (W_F1, 按测试集分布): {w_f1:.4f}%")

    print("="*60)

    return {
        'class_accuracies': class_accuracies,
        'class_f1_scores': f1_scores,
        'overall_accuracy': overall_accuracy,
        'weighted_accuracy': w_acc,
        'macro_f1': macro_f1,
        'weighted_f1': w_f1
    }


