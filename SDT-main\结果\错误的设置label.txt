C:\Users\<USER>\anaconda3\envs\pytorch\python.exe -X pycache_prefix=C:\Users\<USER>\AppData\Local\JetBrains\PyCharm2024.2\cpython-cache "D:/dev/pycharm/PyCharm 2024.2.1/plugins/python-ce/helpers/pydev/pydevd.py" --multiprocess --qt-support=auto --client 127.0.0.1 --port 10008 --file D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py
已连接到 pydev 调试器(内部版本号 242.21829.153)Namespace(no_cuda=False, lr=0.0001, l2=1e-05, dropout=0.5, batch_size=16, hidden_dim=1024, n_head=8, epochs=150, temp=1, tensorboard=False, class_weight=True, Dataset='IEMOCAP')
Running on GPU
temp 1
total parameters: 79687704
training parameters: 79687704
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 1, train_loss: nan, train_acc: 12.62, train_fscore: 12.59, test_loss: nan, test_acc: 45.24, test_fscore: 28.19, time: 65.79 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 2, train_loss: nan, train_acc: 45.55, train_fscore: 28.51, test_loss: nan, test_acc: 45.24, test_fscore: 28.19, time: 67.49 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 3, train_loss: nan, train_acc: 44.64, train_fscore: 27.55, test_loss: nan, test_acc: 48.13, test_fscore: 33.98, time: 66.63 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 4, train_loss: nan, train_acc: 44.82, train_fscore: 28.09, test_loss: nan, test_acc: 57.83, test_fscore: 48.1, time: 65.4 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 5, train_loss: nan, train_acc: 55.39, train_fscore: 44.16, test_loss: nan, test_acc: 58.39, test_fscore: 52.26, time: 66.08 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 6, train_loss: nan, train_acc: 59.32, train_fscore: 52.13, test_loss: nan, test_acc: 58.39, test_fscore: 54.18, time: 66.79 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 7, train_loss: nan, train_acc: 61.6, train_fscore: 57.73, test_loss: nan, test_acc: 59.09, test_fscore: 57.43, time: 66.11 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 8, train_loss: nan, train_acc: 65.35, train_fscore: 64.67, test_loss: nan, test_acc: 59.83, test_fscore: 61.33, time: 66.11 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 9, train_loss: nan, train_acc: 67.46, train_fscore: 68.3, test_loss: nan, test_acc: 60.64, test_fscore: 63.17, time: 67.79 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 10, train_loss: nan, train_acc: 66.72, train_fscore: 66.61, test_loss: nan, test_acc: 63.38, test_fscore: 64.24, time: 66.27 sec
----------best F-Score: 64.24
              precision    recall  f1-score   support

           0     0.9945    0.8830    0.9354      1222
           1     0.7308    0.5429    0.6230       245
           2     0.3469    0.4453    0.3900       384
           3     0.2147    0.8588    0.3435       170
           4     0.7083    0.5117    0.5942       299
           5     0.6667    0.0787    0.1408       381

    accuracy                         0.6338      2701
   macro avg     0.6103    0.5534    0.5045      2701
weighted avg     0.7515    0.6338    0.6424      2701

[[1079   17   38   36   52    0]
 [   0  133   74   29    3    6]
 [   2   26  171  176    4    5]
 [   0    0   22  146    0    2]
 [   4    3   86   51  153    2]
 [   0    3  102  242    4   30]]
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 11, train_loss: nan, train_acc: 68.1, train_fscore: 66.39, test_loss: nan, test_acc: 67.6, test_fscore: 67.03, time: 67.13 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 12, train_loss: nan, train_acc: 69.9, train_fscore: 67.16, test_loss: nan, test_acc: 73.27, test_fscore: 73.67, time: 67.74 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 13, train_loss: nan, train_acc: 72.07, train_fscore: 70.9, test_loss: nan, test_acc: 75.16, test_fscore: 75.67, time: 66.71 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 14, train_loss: nan, train_acc: 73.8, train_fscore: 73.59, test_loss: nan, test_acc: 75.38, test_fscore: 75.23, time: 67.04 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 15, train_loss: nan, train_acc: 72.94, train_fscore: 71.99, test_loss: nan, test_acc: 73.75, test_fscore: 72.76, time: 66.34 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 16, train_loss: nan, train_acc: 69.65, train_fscore: 67.29, test_loss: nan, test_acc: 72.57, test_fscore: 71.06, time: 66.2 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 17, train_loss: nan, train_acc: 71.07, train_fscore: 68.03, test_loss: nan, test_acc: 73.27, test_fscore: 72.22, time: 67.4 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 18, train_loss: nan, train_acc: 72.79, train_fscore: 70.45, test_loss: nan, test_acc: 73.57, test_fscore: 72.84, time: 67.3 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 19, train_loss: nan, train_acc: 73.59, train_fscore: 72.15, test_loss: nan, test_acc: 73.57, test_fscore: 73.28, time: 66.25 sec
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\dataloader.py:20: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at C:\cb\pytorch_1000000000000\work\torch\csrc\utils\tensor_new.cpp:281.)
  return torch.FloatTensor(self.videoText[vid]),\
D:\dev\pycharm\emotion_recognition\SDT-main\SDT-main\train.py:148: RuntimeWarning: invalid value encountered in scalar divide
  avg_loss = round(np.sum(losses) / len(losses), 4)
epoch: 20, train_loss: nan, train_acc: 75.56, train_fscore: 74.71, test_loss: nan, test_acc: 73.68, test_fscore: 73.6, time: 68.15 sec
----------best F-Score: 75.67
              precision    recall  f1-score   support

           0     0.9584    0.9051    0.9310      1222
           1     0.7559    0.6571    0.7031       245
           2     0.4882    0.6458    0.5561       384
           3     0.6149    0.6294    0.6221       170
           4     0.6142    0.6923    0.6509       299
           5     0.6381    0.5276    0.5776       381

    accuracy                         0.7516      2701
   macro avg     0.6783    0.6762    0.6735      2701
weighted avg     0.7683    0.7516    0.7567      2701
[[1106   15   31    4   65    1]
 [   2  161   52    3    9   18]
 [  16   28  248   14   18   60]
 [   1    0   19  107   12   31]
 [  26    3   56    3  207    4]
 [   3    6  102   43   26  201]]
epoch: 21, train_loss: nan, train_acc: 76.19, train_fscore: 75.84, test_loss: nan, test_acc: 72.23, test_fscore: 72.15, time: 65.77 sec

